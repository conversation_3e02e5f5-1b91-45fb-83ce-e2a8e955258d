/**
 * 小柿子·幼儿识字辅助工具
 * 微信小程序主入口文件
 */

// 引入数据管理模块
const DataManager = require('./utils/dataManager.js');
const EbbinghausManager = require('./utils/ebbinghausManager.js');

App({
  /**
   * 小程序初始化
   */
  onLaunch: function () {
    console.log('小柿子识字工具启动');
    
    // 初始化数据管理器
    this.dataManager = new DataManager();
    this.ebbinghausManager = new EbbinghausManager();
    
    // 检查是否首次启动
    this.checkFirstLaunch();
    
    // 初始化用户数据
    this.initUserData();
  },

  /**
   * 检查是否首次启动
   */
  checkFirstLaunch: function() {
    const hasLaunched = wx.getStorageSync('hasLaunched');
    if (!hasLaunched) {
      this.globalData.isFirstLaunch = true;
      wx.setStorageSync('hasLaunched', true);
    }
  },

  /**
   * 初始化用户数据
   */
  initUserData: function() {
    try {
      // 获取用户信息
      const userInfo = this.dataManager.getUserInfo();
      if (userInfo) {
        this.globalData.userInfo = userInfo;
        this.globalData.currentChild = userInfo.children_info[0] || null;
      }
    } catch (error) {
      console.error('初始化用户数据失败:', error);
    }
  },

  /**
   * 小程序显示
   */
  onShow: function () {
    // 更新艾宾浩斯复习计划
    this.updateReviewPlans();
  },

  /**
   * 更新复习计划
   */
  updateReviewPlans: function() {
    if (this.globalData.currentChild) {
      this.ebbinghausManager.updateReviewPlans(this.globalData.currentChild.child_id);
    }
  },

  /**
   * 获取当前选中的幼儿
   */
  getCurrentChild: function() {
    return this.globalData.currentChild;
  },

  /**
   * 设置当前选中的幼儿
   */
  setCurrentChild: function(child) {
    this.globalData.currentChild = child;
    // 更新该幼儿的复习计划
    this.ebbinghausManager.updateReviewPlans(child.child_id);
  },

  /**
   * 全局数据
   */
  globalData: {
    userInfo: null,
    currentChild: null,
    isFirstLaunch: false,
    version: '1.0.0'
  }
});
