/**
 * 我的页面 - 个人中心
 */

const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    currentChild: null,
    userInfo: null,
    totalWords: 0,
    masteredWords: 0,
    studyDays: 0,
    menuItems: [
      {
        icon: '👶',
        title: '幼儿信息管理',
        desc: '添加、编辑幼儿基本信息',
        url: '/pages/child-management/child-management'
      },
      {
        icon: '📚',
        title: '字词库管理',
        desc: '查看和管理所有字词',
        url: '/pages/word-list/word-list'
      },
      {
        icon: '📊',
        title: '学习记录',
        desc: '查看详细的学习统计',
        url: '/pages/learning-records/learning-records'
      },
      {
        icon: '⚙️',
        title: '设置',
        desc: '应用设置和数据管理',
        url: '/pages/settings/settings'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.loadUserData();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.loadUserData();
  },

  /**
   * 加载用户数据
   */
  loadUserData: function() {
    const userInfo = app.dataManager.getUserInfo();
    const currentChild = app.getCurrentChild();
    
    this.setData({
      userInfo,
      currentChild
    });

    if (currentChild) {
      this.loadChildStats();
    }
  },

  /**
   * 加载幼儿统计数据
   */
  loadChildStats: function() {
    const childId = this.data.currentChild.child_id;
    
    // 获取字词统计
    const wordLibrary = app.dataManager.getWordLibrary(childId);
    const totalWords = wordLibrary.length;
    const masteredWords = wordLibrary.filter(word => word.mastered_status).length;
    
    // 获取学习天数
    const learningRecords = app.dataManager.getLearningRecords(childId);
    const uniqueDates = [...new Set(learningRecords.map(record => record.learn_date))];
    const studyDays = uniqueDates.length;

    this.setData({
      totalWords,
      masteredWords,
      studyDays
    });
  },

  /**
   * 切换幼儿
   */
  switchChild: function() {
    if (!this.data.userInfo || this.data.userInfo.children_info.length === 0) {
      wx.navigateTo({
        url: '/pages/child-management/child-management?action=add'
      });
      return;
    }

    if (this.data.userInfo.children_info.length === 1) {
      wx.showToast({
        title: '只有一个幼儿',
        icon: 'none'
      });
      return;
    }

    // 显示幼儿选择列表
    const children = this.data.userInfo.children_info;
    const itemList = children.map(child => `${child.child_name} (${child.class_type})`);
    
    wx.showActionSheet({
      itemList,
      success: (res) => {
        const selectedChild = children[res.tapIndex];
        app.setCurrentChild(selectedChild);
        this.loadUserData();
        
        wx.showToast({
          title: `已切换到 ${selectedChild.child_name}`,
          icon: 'success'
        });
      }
    });
  },

  /**
   * 菜单项点击
   */
  onMenuItemTap: function(e) {
    const url = e.currentTarget.dataset.url;
    wx.navigateTo({
      url
    });
  },

  /**
   * 分享小程序
   */
  shareApp: function() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  /**
   * 意见反馈
   */
  feedback: function() {
    wx.showModal({
      title: '意见反馈',
      content: '如有问题或建议，请关注我们的公众号进行反馈',
      confirmText: '好的',
      showCancel: false
    });
  },

  /**
   * 关于我们
   */
  aboutUs: function() {
    wx.showModal({
      title: '关于小柿子识字',
      content: '小柿子·幼儿识字辅助工具\n版本：1.0.0\n\n专为3-6岁幼儿设计的识字学习工具，基于艾宾浩斯遗忘曲线科学规划复习，让识字学习更高效。',
      confirmText: '好的',
      showCancel: false
    });
  },

  /**
   * 分享给朋友
   */
  onShareAppMessage: function() {
    return {
      title: '小柿子识字 - 让幼儿识字更高效',
      desc: '基于艾宾浩斯遗忘曲线的科学复习工具',
      path: '/pages/onboarding/onboarding'
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline: function() {
    return {
      title: '小柿子识字 - 让幼儿识字更高效'
    };
  }
});
