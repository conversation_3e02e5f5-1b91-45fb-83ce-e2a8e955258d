<!--复习页面 - 历史复习-->
<view class="review-container">
  <!-- 复习统计卡片 -->
  <view class="card stats-card">
    <view class="card-title">📊 复习统计</view>
    <view class="stats-grid">
      <view class="stat-item">
        <view class="stat-number primary">{{reviewStats.mastered_words}}</view>
        <view class="stat-label">已掌握</view>
      </view>
      <view class="stat-item">
        <view class="stat-number orange">{{reviewStats.total_words - reviewStats.mastered_words}}</view>
        <view class="stat-label">学习中</view>
      </view>
      <view class="stat-item">
        <view class="stat-number blue">{{reviewStats.mastery_rate}}%</view>
        <view class="stat-label">掌握率</view>
      </view>
    </view>
  </view>

  <!-- 今日复习任务 -->
  <view class="card">
    <view class="card-header">
      <view class="card-title">📅 今日复习</view>
      <view class="task-count">{{reviewStats.today_completed}}/{{reviewStats.today_total}}</view>
    </view>
    
    <view wx:if="{{todayTasks.length > 0}}" class="today-tasks">
      <view class="task-progress">
        <view class="progress-bar">
          <view 
            class="progress-fill" 
            style="width: {{reviewStats.today_total > 0 ? (reviewStats.today_completed / reviewStats.today_total * 100) : 0}}%"
          ></view>
        </view>
        <text class="progress-text">
          {{reviewStats.today_total > 0 ? '还有 ' + (reviewStats.today_total - reviewStats.today_completed) + ' 个字词待复习' : '今日任务已完成'}}
        </text>
      </view>
      
      <view class="task-list">
        <view 
          wx:for="{{todayTasks}}" 
          wx:key="index" 
          class="task-item"
        >
          <view class="task-word">{{item.word_info.word_text}}</view>
          <view class="task-stage">第{{item.review_stage}}次复习</view>
        </view>
      </view>
      
      <button class="btn-primary start-review-btn" bindtap="startTodayReview">
        开始今日复习
      </button>
    </view>
    
    <view wx:else class="empty-tasks">
      <view class="empty-icon">✅</view>
      <view class="empty-text">今日复习任务已完成</view>
      <view class="empty-desc">明天继续加油！</view>
    </view>
  </view>

  <!-- 本周复习日历 -->
  <view class="card">
    <view class="card-header">
      <view class="card-title">📆 本周复习</view>
      <text class="view-more" bindtap="viewCalendar">查看更多 →</text>
    </view>
    
    <view class="weekly-calendar">
      <view 
        wx:for="{{weeklyCalendar}}" 
        wx:key="dateStr" 
        class="calendar-day {{item.isToday ? 'today' : ''}} {{item.total > 0 ? 'has-tasks' : ''}}"
        data-date="{{item.dateStr}}"
        bindtap="onCalendarDateTap"
      >
        <view class="day-name">{{item.dayName}}</view>
        <view class="day-number">{{item.date}}</view>
        <view class="day-tasks" wx:if="{{item.total > 0}}">
          <view class="task-dot {{item.completed === item.total ? 'completed' : 'pending'}}"></view>
          <text class="task-count">{{item.total}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 薄弱字词专项 -->
  <view class="card" wx:if="{{weakWords.length > 0}}">
    <view class="card-header">
      <view class="card-title">⚠️ 薄弱字词专项</view>
      <text class="weak-count">{{weakWords.length}}个</text>
    </view>
    
    <view class="weak-words-list">
      <view 
        wx:for="{{weakWords}}" 
        wx:key="word_id" 
        class="weak-word-item"
      >
        <view class="weak-word-text">{{item.word_text}}</view>
        <view class="weak-word-times">复习{{item.review_times}}次</view>
      </view>
    </view>
    
    <button class="btn-secondary start-weak-review-btn" bindtap="startWeakWordsReview">
      开始专项复习
    </button>
  </view>

  <!-- 学习记录入口 -->
  <view class="card">
    <view class="card-title">📈 学习记录</view>
    <view class="record-summary">
      <view class="summary-item">
        <view class="summary-label">本周学习天数</view>
        <view class="summary-value">{{reviewStats.week_completed}}天</view>
      </view>
      <view class="summary-item">
        <view class="summary-label">总学习字词</view>
        <view class="summary-value">{{reviewStats.total_words}}个</view>
      </view>
    </view>
    
    <button class="btn-outline view-records-btn" bindtap="viewLearningRecords">
      查看详细记录
    </button>
  </view>
</view>
