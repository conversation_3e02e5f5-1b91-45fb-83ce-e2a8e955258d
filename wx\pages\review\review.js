/**
 * 复习页面 - 历史复习
 */

const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    currentChild: null,
    reviewStats: {
      total_words: 0,
      mastered_words: 0,
      today_total: 0,
      today_completed: 0,
      mastery_rate: 0
    },
    todayTasks: [],
    weeklyCalendar: [],
    currentDate: '',
    selectedDate: '',
    weakWords: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.initPage();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.refreshData();
  },

  /**
   * 初始化页面
   */
  initPage: function() {
    const currentChild = app.getCurrentChild();
    if (!currentChild) {
      this.showNoChildTip();
      return;
    }

    this.setData({
      currentChild,
      currentDate: app.dataManager.getCurrentDate()
    });

    this.refreshData();
  },

  /**
   * 显示无幼儿提示
   */
  showNoChildTip: function() {
    wx.showModal({
      title: '设置幼儿信息',
      content: '请先添加幼儿信息，以便为您提供个性化的复习服务',
      confirmText: '去添加',
      cancelText: '稍后',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/child-management/child-management?action=add'
          });
        }
      }
    });
  },

  /**
   * 刷新数据
   */
  refreshData: function() {
    if (!this.data.currentChild) return;

    this.loadReviewStats();
    this.loadTodayTasks();
    this.loadWeeklyCalendar();
    this.loadWeakWords();
  },

  /**
   * 加载复习统计
   */
  loadReviewStats: function() {
    const childId = this.data.currentChild.child_id;
    const stats = app.ebbinghausManager.getReviewStatistics(childId);
    
    this.setData({
      reviewStats: stats
    });
  },

  /**
   * 加载今日任务
   */
  loadTodayTasks: function() {
    const childId = this.data.currentChild.child_id;
    const todayTasks = app.ebbinghausManager.getTodayReviewTasks(childId);
    
    this.setData({
      todayTasks: todayTasks.slice(0, 5) // 只显示前5个
    });
  },

  /**
   * 加载本周日历
   */
  loadWeeklyCalendar: function() {
    const childId = this.data.currentChild.child_id;
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    
    const calendar = app.ebbinghausManager.getReviewCalendar(childId, year, month);
    
    // 生成本周日历数据
    const weeklyCalendar = this.generateWeeklyCalendar(calendar);
    
    this.setData({
      weeklyCalendar
    });
  },

  /**
   * 生成本周日历数据
   */
  generateWeeklyCalendar: function(calendar) {
    const now = new Date();
    const currentDate = now.getDate();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();
    
    // 获取本周的日期范围
    const startOfWeek = new Date(now);
    startOfWeek.setDate(currentDate - now.getDay() + 1); // 周一开始
    
    const weekDays = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);
      
      const dateStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
      const dayData = calendar[dateStr] || { total: 0, completed: 0, stages: {} };
      
      weekDays.push({
        date: date.getDate(),
        dateStr: dateStr,
        dayName: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'][i],
        isToday: date.toDateString() === now.toDateString(),
        isPast: date < now,
        isFuture: date > now,
        ...dayData
      });
    }
    
    return weekDays;
  },

  /**
   * 加载薄弱字词
   */
  loadWeakWords: function() {
    const childId = this.data.currentChild.child_id;
    const weakWords = app.ebbinghausManager.getWeakWords(childId);
    
    this.setData({
      weakWords: weakWords.slice(0, 6) // 只显示前6个
    });
  },

  /**
   * 开始今日复习
   */
  startTodayReview: function() {
    if (this.data.todayTasks.length === 0) {
      wx.showToast({
        title: '暂无复习任务',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/word-review/word-review?type=today'
    });
  },

  /**
   * 开始薄弱字词专项复习
   */
  startWeakWordsReview: function() {
    if (this.data.weakWords.length === 0) {
      wx.showToast({
        title: '暂无薄弱字词',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/word-review/word-review?type=weak'
    });
  },

  /**
   * 查看复习日历
   */
  viewCalendar: function() {
    wx.navigateTo({
      url: '/pages/review-calendar/review-calendar'
    });
  },

  /**
   * 查看学习记录
   */
  viewLearningRecords: function() {
    wx.navigateTo({
      url: '/pages/learning-records/learning-records'
    });
  },

  /**
   * 日历日期点击
   */
  onCalendarDateTap: function(e) {
    const dateStr = e.currentTarget.dataset.date;
    this.setData({
      selectedDate: dateStr
    });
    
    // 可以在这里显示该日期的详细复习任务
    this.showDateTasks(dateStr);
  },

  /**
   * 显示指定日期的任务
   */
  showDateTasks: function(dateStr) {
    const childId = this.data.currentChild.child_id;
    const allPlans = app.dataManager.getReviewPlans(childId);
    const dateTasks = allPlans.filter(plan => plan.review_date === dateStr);
    
    if (dateTasks.length === 0) {
      wx.showToast({
        title: '该日期无复习任务',
        icon: 'none'
      });
      return;
    }

    const taskTexts = dateTasks.map(task => {
      const word = app.dataManager.getWordLibrary(childId)
        .find(w => w.word_id === task.word_id);
      return word ? word.word_text : '未知字词';
    });

    wx.showModal({
      title: `${dateStr} 复习任务`,
      content: `共 ${dateTasks.length} 个字词：\n${taskTexts.join('、')}`,
      showCancel: false
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function() {
    this.refreshData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  }
});
