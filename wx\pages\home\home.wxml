<!--首页 - 今日复习-->
<view class="home-container">
  <!-- 欢迎区域 -->
  <view class="welcome-section">
    <view class="welcome-content">
      <view class="welcome-left">
        <view class="greeting">{{greeting}} 👋</view>
        <view class="welcome-subtitle">今天也要加油学习哦</view>
      </view>
      <view class="welcome-right">
        <view class="greeting-icon">{{greetingIcon}}</view>
        <view class="child-info" wx:if="{{currentChild}}" bindtap="onSwitchChild">
          {{currentChild.child_name}} · {{currentChild.class_type}}
        </view>
        <view class="no-child" wx:else bindtap="onSwitchChild">
          点击设置幼儿信息
        </view>
      </view>
    </view>
  </view>

  <!-- 主要内容 -->
  <view class="main-content">
    <!-- 今日任务卡片 -->
    <view class="card">
      <view class="card-header">
        <view class="card-title">📅 今日任务</view>
        <view class="date-tag">{{todayDate}}</view>
      </view>
      
      <!-- 进度显示 -->
      <view class="progress-section">
        <view class="progress-info">
          <text class="progress-label">今日复习进度</text>
          <text class="progress-text">{{todayProgress.completed}}/{{todayProgress.total}} 完成</text>
        </view>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{todayProgress.percentage}}%"></view>
        </view>
      </view>
      
      <!-- 快速操作按钮 -->
      <view class="action-buttons">
        <button class="btn-primary action-btn" bindtap="onAddWords">
          ➕ 添加字词
        </button>
        <button class="btn-secondary action-btn" bindtap="onStartReview">
          ▶️ 开始复习
        </button>
      </view>
    </view>

    <!-- 今日字词列表 -->
    <view class="card" wx:if="{{todayWords.length > 0}}">
      <view class="card-header">
        <view class="card-title">📝 今日字词</view>
        <text class="word-count">{{todayProgress.total}}个字词</text>
      </view>
      
      <view class="word-list">
        <view 
          wx:for="{{todayWords}}" 
          wx:key="index" 
          class="word-item {{item.word_info.mastered_status ? 'completed' : 'pending'}}"
        >
          <view class="word-display">
            <view class="word-char">{{item.word_info.word_text}}</view>
            <view class="word-info">
              <view class="word-name">{{item.word_info.word_text}}</view>
              <view class="word-status">
                {{item.word_info.mastered_status ? '已掌握' : (item.isNewWord ? '新字词' : '待复习')}}
              </view>
            </view>
          </view>
          <view class="word-status-icon">
            {{item.word_info.mastered_status ? '✅' : '⏰'}}
          </view>
        </view>
      </view>
      
      <view class="view-all-btn" bindtap="onViewAllWords" wx:if="{{todayProgress.total > 4}}">
        查看全部 {{todayProgress.total}} 个字词 →
      </view>
    </view>

    <!-- 无字词提示 -->
    <view class="card empty-state" wx:if="{{todayWords.length === 0}}">
      <view class="empty-icon">📚</view>
      <view class="empty-title">暂无今日字词</view>
      <view class="empty-desc">点击"添加字词"开始今天的学习吧</view>
      <button class="btn-primary" bindtap="onAddWords">添加字词</button>
    </view>

    <!-- 学习统计 -->
    <view class="card">
      <view class="card-title">📊 本周学习</view>
      <view class="stats-grid">
        <view class="stat-item">
          <view class="stat-number">{{weeklyStats.learnedWords}}</view>
          <view class="stat-label">已学字词</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{weeklyStats.pendingReview}}</view>
          <view class="stat-label">待复习</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{weeklyStats.studyDays}}</view>
          <view class="stat-label">学习天数</view>
        </view>
      </view>
    </view>
  </view>
</view>
