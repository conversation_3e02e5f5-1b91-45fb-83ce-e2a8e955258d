/* 设置页面样式 */

.settings-container {
  background: var(--warm-beige);
  min-height: 100vh;
  padding: 32rpx;
}

/* 当前幼儿卡片 */
.current-child-card {
  background: linear-gradient(135deg, var(--primary-green), var(--light-green));
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  color: white;
  box-shadow: var(--shadow-soft);
}

.child-info {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.child-avatar {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.child-details {
  flex: 1;
}

.child-name {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.child-meta {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32rpx;
  text-align: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.stat-number {
  font-size: 40rpx;
  font-weight: bold;
  color: var(--primary-green);
}

.stat-label {
  font-size: 24rpx;
  color: var(--text-light);
}

/* 存储信息 */
.storage-info {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.storage-text {
  font-size: 28rpx;
  color: var(--text-dark);
  text-align: center;
}

.storage-bar {
  height: 16rpx;
  background: var(--border-light);
  border-radius: 8rpx;
  overflow: hidden;
}

.storage-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-green), var(--light-green));
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

.storage-percentage {
  font-size: 24rpx;
  color: var(--text-light);
  text-align: center;
}

/* 设置列表 */
.settings-list {
  display: flex;
  flex-direction: column;
}

.setting-item {
  display: flex;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1px solid var(--border-light);
  transition: background-color 0.3s ease;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:active {
  background: var(--warm-beige);
  margin: 0 -32rpx;
  padding-left: 32rpx;
  padding-right: 32rpx;
  border-radius: 16rpx;
}

.setting-icon {
  width: 64rpx;
  height: 64rpx;
  background: var(--soft-green);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-right: 24rpx;
}

.setting-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.setting-title {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-dark);
}

.setting-desc {
  font-size: 24rpx;
  color: var(--text-light);
  line-height: 1.4;
}

.setting-arrow {
  font-size: 28rpx;
  color: var(--text-light);
  margin-left: 16rpx;
}

/* 危险操作卡片 */
.danger-card {
  border: 2px solid #ffcdd2;
  background: #ffebee;
}

.danger-title {
  color: #d32f2f;
}

.danger-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
}

.danger-icon {
  width: 64rpx;
  height: 64rpx;
  background: #ffcdd2;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-right: 24rpx;
}

.danger-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.danger-content .danger-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #d32f2f;
}

.danger-desc {
  font-size: 24rpx;
  color: #f44336;
  line-height: 1.4;
}

.danger-arrow {
  font-size: 28rpx;
  color: #f44336;
  margin-left: 16rpx;
}

/* 数据安全提示 */
.data-notice {
  background: #fff3e0;
  border: 2px solid #ffe0b2;
  border-radius: 24rpx;
  padding: 32rpx;
  margin: 32rpx 0;
  display: flex;
  gap: 20rpx;
}

.notice-icon {
  font-size: 40rpx;
  flex-shrink: 0;
}

.notice-content {
  flex: 1;
}

.notice-title {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--soft-orange);
  margin-bottom: 12rpx;
}

.notice-text {
  font-size: 24rpx;
  color: #e65100;
  line-height: 1.6;
}

/* 版本信息 */
.version-info {
  text-align: center;
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.version-info text {
  color: var(--text-light);
  font-size: 24rpx;
}

.version-info text:first-child {
  font-weight: 500;
  font-size: 26rpx;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .child-info {
    flex-direction: column;
    text-align: center;
    gap: 16rpx;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 24rpx;
  }
  
  .setting-item {
    padding: 24rpx 0;
  }
  
  .setting-icon, .danger-icon {
    width: 48rpx;
    height: 48rpx;
    font-size: 24rpx;
  }
}
