<!--幼儿信息管理页面-->
<view class="child-management-container">
  
  <!-- 列表模式 -->
  <view wx:if="{{action === 'list'}}" class="list-mode">
    <!-- 页面标题 -->
    <view class="page-header">
      <view class="page-title">👶 幼儿信息管理</view>
      <view class="page-desc">管理和切换幼儿账号</view>
    </view>

    <!-- 幼儿列表 -->
    <view class="children-list" wx:if="{{children.length > 0}}">
      <view 
        wx:for="{{children}}" 
        wx:key="child_id" 
        class="child-item"
      >
        <view class="child-info" data-child-id="{{item.child_id}}" bindtap="selectChild">
          <view class="child-avatar">👶</view>
          <view class="child-details">
            <view class="child-name">{{item.child_name}}</view>
            <view class="child-meta">{{item.class_type}} · {{item.age}}岁</view>
            <view class="child-birthday">生日：{{item.birthday}}</view>
          </view>
          <view class="child-status" wx:if="{{item.child_id === currentChild.child_id}}">
            <text class="current-tag">当前</text>
          </view>
        </view>
        
        <view class="child-actions">
          <button 
            class="action-btn edit-btn" 
            data-child-id="{{item.child_id}}"
            bindtap="editChild"
          >
            编辑
          </button>
          <button 
            class="action-btn delete-btn" 
            data-child-id="{{item.child_id}}"
            bindtap="deleteChild"
          >
            删除
          </button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:else>
      <view class="empty-icon">👶</view>
      <view class="empty-title">暂无幼儿信息</view>
      <view class="empty-desc">添加幼儿信息，开始个性化学习</view>
    </view>

    <!-- 添加按钮 -->
    <view class="add-button-container">
      <button class="btn-primary add-child-btn" bindtap="switchToAdd">
        ➕ 添加幼儿
      </button>
    </view>
  </view>

  <!-- 添加/编辑模式 -->
  <view wx:else class="form-mode">
    <!-- 表单标题 -->
    <view class="form-header">
      <view class="form-title">
        {{action === 'add' ? '➕ 添加幼儿' : '✏️ 编辑幼儿'}}
      </view>
      <view class="form-desc">
        {{action === 'add' ? '请填写幼儿的基本信息' : '修改幼儿的基本信息'}}
      </view>
    </view>

    <!-- 表单内容 -->
    <view class="form-content">
      <!-- 姓名输入 -->
      <view class="form-group">
        <view class="form-label">👶 幼儿姓名</view>
        <input 
          class="form-input"
          placeholder="请输入幼儿姓名"
          value="{{formData.name}}"
          bindinput="onNameInput"
          maxlength="10"
        />
      </view>

      <!-- 生日选择 -->
      <view class="form-group">
        <view class="form-label">🎂 出生年月</view>
        <view class="birthday-selectors">
          <picker 
            class="birthday-picker"
            range="{{years}}"
            value="{{yearIndex}}"
            bindchange="onYearChange"
          >
            <view class="picker-display {{yearIndex !== null ? 'selected' : ''}}">
              {{years[yearIndex] || '选择年份'}}
            </view>
          </picker>
          
          <picker 
            class="birthday-picker"
            range="{{months}}"
            value="{{monthIndex}}"
            bindchange="onMonthChange"
          >
            <view class="picker-display {{monthIndex !== null ? 'selected' : ''}}">
              {{months[monthIndex] || '选择月份'}}
            </view>
          </picker>
        </view>
      </view>

      <!-- 班级选择 -->
      <view class="form-group">
        <view class="form-label">🏫 所在班级</view>
        <picker 
          class="class-picker"
          range="{{classTypes}}"
          value="{{classIndex}}"
          bindchange="onClassChange"
        >
          <view class="picker-display full-width {{classIndex !== null ? 'selected' : ''}}">
            {{classTypes[classIndex] || '请选择班级'}}
          </view>
        </picker>
      </view>

      <!-- 年龄显示 -->
      <view class="age-display" wx:if="{{formData.birthday}}">
        <view class="age-label">📅 当前年龄</view>
        <view class="age-value">
          {{formData.age}}岁
        </view>
      </view>
    </view>

    <!-- 表单操作 -->
    <view class="form-actions">
      <button class="btn-outline cancel-btn" bindtap="switchToList">
        取消
      </button>
      <button class="btn-primary save-btn" bindtap="saveChild">
        {{action === 'add' ? '添加' : '保存'}}
      </button>
    </view>
  </view>

  <!-- 使用说明 -->
  <view class="usage-tips" wx:if="{{action === 'list'}}">
    <view class="tips-title">💡 使用说明</view>
    <view class="tips-content">
      <view class="tip-item">• 支持管理多个幼儿的学习数据</view>
      <view class="tip-item">• 点击幼儿卡片可切换当前学习对象</view>
      <view class="tip-item">• 删除幼儿会同时删除所有相关学习数据</view>
      <view class="tip-item">• 年龄会根据生日自动计算</view>
    </view>
  </view>
</view>
