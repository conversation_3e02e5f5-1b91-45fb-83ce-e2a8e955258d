/* 我的页面样式 */

.profile-container {
  background: var(--warm-beige);
  min-height: 100vh;
  padding: 32rpx;
}

/* 用户信息卡片 */
.user-card {
  background: linear-gradient(135deg, var(--primary-green), var(--light-green));
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  color: white;
  box-shadow: var(--shadow-soft);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.avatar-icon {
  font-size: 60rpx;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.user-meta {
  font-size: 28rpx;
  opacity: 0.9;
}

.switch-btn {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.switch-btn:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 0.3);
}

.switch-icon {
  font-size: 32rpx;
}

/* 学习统计 */
.stats-section {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 32rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32rpx;
  text-align: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 功能菜单 */
.menu-section, .other-section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 24rpx;
  padding-left: 8rpx;
}

.menu-list, .other-list {
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: var(--shadow-soft);
  border: 1px solid var(--border-light);
}

.menu-item, .other-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1px solid var(--border-light);
  transition: background-color 0.3s ease;
}

.menu-item:last-child, .other-item:last-child {
  border-bottom: none;
}

.menu-item:active, .other-item:active {
  background: var(--warm-beige);
}

.menu-icon, .other-icon {
  width: 80rpx;
  height: 80rpx;
  background: var(--soft-green);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-right: 24rpx;
}

.menu-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.menu-title, .other-title {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-dark);
}

.menu-desc {
  font-size: 24rpx;
  color: var(--text-light);
  line-height: 1.4;
}

.menu-arrow, .other-arrow {
  font-size: 28rpx;
  color: var(--text-light);
  margin-left: 16rpx;
}

/* 数据提示 */
.data-notice {
  background: #FFF3E0;
  border: 1px solid #FFE0B2;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  display: flex;
  gap: 20rpx;
}

.notice-icon {
  font-size: 40rpx;
  flex-shrink: 0;
}

.notice-content {
  flex: 1;
}

.notice-title {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--soft-orange);
  margin-bottom: 12rpx;
}

.notice-text {
  font-size: 24rpx;
  color: #E65100;
  line-height: 1.5;
}

/* 版本信息 */
.version-info {
  text-align: center;
  padding: 32rpx;
  color: var(--text-light);
  font-size: 24rpx;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .user-info {
    flex-direction: column;
    text-align: center;
    gap: 16rpx;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 24rpx;
  }
  
  .menu-item, .other-item {
    padding: 24rpx;
  }
  
  .menu-icon, .other-icon {
    width: 60rpx;
    height: 60rpx;
    font-size: 32rpx;
  }
}
