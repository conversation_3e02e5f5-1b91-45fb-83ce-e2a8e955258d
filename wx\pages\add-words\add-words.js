/**
 * 添加字词页面
 */

const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    inputText: '',
    wordList: [],
    currentChild: null,
    separators: ['，', '、', ' ', '\n', '；'], // 支持的分隔符
    showPreview: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    const currentChild = app.getCurrentChild();
    if (!currentChild) {
      wx.showToast({
        title: '请先设置幼儿信息',
        icon: 'none'
      });
      wx.navigateBack();
      return;
    }

    this.setData({
      currentChild
    });
  },

  /**
   * 输入框内容变化
   */
  onInputChange: function(e) {
    const inputText = e.detail.value;
    this.setData({
      inputText
    });

    // 实时解析字词
    this.parseWords(inputText);
  },

  /**
   * 解析字词
   */
  parseWords: function(text) {
    if (!text.trim()) {
      this.setData({
        wordList: [],
        showPreview: false
      });
      return;
    }

    let words = [text];
    
    // 使用多种分隔符分割
    this.data.separators.forEach(separator => {
      const newWords = [];
      words.forEach(word => {
        newWords.push(...word.split(separator));
      });
      words = newWords;
    });

    // 清理和去重
    words = words
      .map(word => word.trim())
      .filter(word => word.length > 0)
      .filter((word, index, arr) => arr.indexOf(word) === index);

    this.setData({
      wordList: words,
      showPreview: words.length > 0
    });
  },

  /**
   * 批量添加字词
   */
  addBatchWords: function() {
    if (this.data.wordList.length === 0) {
      wx.showToast({
        title: '请输入字词',
        icon: 'none'
      });
      return;
    }

    this.addWords(this.data.wordList);
  },

  /**
   * 添加字词到数据库
   */
  addWords: function(words) {
    wx.showLoading({
      title: '添加中...'
    });

    const childId = this.data.currentChild.child_id;
    
    // 检查重复字词
    const existingWords = app.dataManager.getWordLibrary(childId);
    const existingTexts = existingWords.map(word => word.word_text);
    
    const newWords = words.filter(word => !existingTexts.includes(word));
    const duplicateWords = words.filter(word => existingTexts.includes(word));

    if (newWords.length === 0) {
      wx.hideLoading();
      wx.showModal({
        title: '提示',
        content: '所有字词都已存在，无需重复添加',
        showCancel: false
      });
      return;
    }

    // 添加新字词
    const success = app.dataManager.addWords(childId, newWords);
    
    wx.hideLoading();

    if (success) {
      let message = `成功添加 ${newWords.length} 个字词`;
      if (duplicateWords.length > 0) {
        message += `，${duplicateWords.length} 个字词已存在`;
      }

      wx.showModal({
        title: '添加成功',
        content: message,
        confirmText: '开始复习',
        cancelText: '继续添加',
        success: (res) => {
          if (res.confirm) {
            // 跳转到复习页面
            wx.navigateTo({
              url: '/pages/word-review/word-review?type=today'
            });
          } else {
            // 清空输入，继续添加
            this.setData({
              inputText: '',
              wordList: [],
              showPreview: false
            });
          }
        }
      });
    } else {
      wx.showToast({
        title: '添加失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 删除预览中的字词
   */
  removeWord: function(e) {
    const index = e.currentTarget.dataset.index;
    const wordList = this.data.wordList;
    wordList.splice(index, 1);
    
    this.setData({
      wordList,
      showPreview: wordList.length > 0
    });
  },

  /**
   * 清空输入
   */
  clearInput: function() {
    this.setData({
      inputText: '',
      wordList: [],
      showPreview: false
    });
  },

  /**
   * 使用示例数据
   */
  useExample: function() {
    const exampleText = '太阳，月亮，星星，白云，小鸟，花朵，小草，大树';
    this.setData({
      inputText: exampleText
    });

    this.parseWords(exampleText);
  },

  /**
   * 查看字词库
   */
  viewWordLibrary: function() {
    wx.navigateTo({
      url: '/pages/word-list/word-list'
    });
  }
});
