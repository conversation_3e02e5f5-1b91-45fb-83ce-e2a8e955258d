<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学习记录 - 小柿子识字工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="common.css">
</head>
<body>
    <div class="phone-container">
        <div class="phone-screen">
            <!-- iOS状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </div>
            </div>
            
            <!-- 小程序导航栏 -->
            <div class="nav-header">
                <i class="nav-back fas fa-chevron-left"></i>
                <span>学习记录</span>
            </div>
            
            <!-- 主内容区域 -->
            <div class="main-content">
                <!-- 统计概览 -->
                <div class="p-4">
                    <div class="card">
                        <h3 class="text-lg font-bold text-gray-800 mb-4">📊 学习统计</h3>
                        
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div class="text-center p-4 bg-green-50 rounded-lg">
                                <div class="text-3xl font-bold text-green-600">156</div>
                                <div class="text-sm text-gray-600">累计学习字词</div>
                            </div>
                            <div class="text-center p-4 bg-blue-50 rounded-lg">
                                <div class="text-3xl font-bold text-blue-600">12</div>
                                <div class="text-sm text-gray-600">连续学习天数</div>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-3 gap-4 text-center">
                            <div>
                                <div class="text-xl font-bold text-orange-600">89%</div>
                                <div class="text-xs text-gray-500">掌握率</div>
                            </div>
                            <div>
                                <div class="text-xl font-bold text-purple-600">24</div>
                                <div class="text-xs text-gray-500">本周学习</div>
                            </div>
                            <div>
                                <div class="text-xl font-bold text-red-600">5</div>
                                <div class="text-xs text-gray-500">薄弱字词</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 时间筛选 -->
                <div class="px-4">
                    <div class="flex gap-2 mb-4">
                        <button class="btn-primary text-sm px-4 py-2">本周</button>
                        <button class="btn-outline text-sm px-4 py-2">本月</button>
                        <button class="btn-outline text-sm px-4 py-2">全部</button>
                    </div>
                </div>
                
                <!-- 学习记录列表 -->
                <div class="px-4">
                    <div class="card">
                        <h3 class="text-lg font-bold text-gray-800 mb-4">📅 学习记录</h3>
                        
                        <div class="space-y-4">
                            <!-- 今天记录 -->
                            <div class="border border-green-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center gap-2">
                                        <span class="font-bold text-gray-800">今天</span>
                                        <span class="text-sm text-gray-500">6月9日</span>
                                    </div>
                                    <span class="tag">进行中</span>
                                </div>
                                
                                <div class="grid grid-cols-3 gap-4 text-center mb-3">
                                    <div>
                                        <div class="text-lg font-bold text-green-600">6</div>
                                        <div class="text-xs text-gray-500">已掌握</div>
                                    </div>
                                    <div>
                                        <div class="text-lg font-bold text-orange-600">2</div>
                                        <div class="text-xs text-gray-500">需复习</div>
                                    </div>
                                    <div>
                                        <div class="text-lg font-bold text-blue-600">8</div>
                                        <div class="text-xs text-gray-500">总计</div>
                                    </div>
                                </div>
                                
                                <div class="mb-2">
                                    <div class="flex justify-between text-sm mb-1">
                                        <span class="text-gray-600">学习进度</span>
                                        <span class="text-green-600">75%</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 75%"></div>
                                    </div>
                                </div>
                                
                                <div class="text-xs text-gray-500">
                                    薄弱字词：花、草
                                </div>
                            </div>
                            
                            <!-- 昨天记录 -->
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center gap-2">
                                        <span class="font-bold text-gray-800">昨天</span>
                                        <span class="text-sm text-gray-500">6月8日</span>
                                    </div>
                                    <span class="tag" style="background: #10B981; color: white;">已完成</span>
                                </div>
                                
                                <div class="grid grid-cols-3 gap-4 text-center mb-3">
                                    <div>
                                        <div class="text-lg font-bold text-green-600">10</div>
                                        <div class="text-xs text-gray-500">已掌握</div>
                                    </div>
                                    <div>
                                        <div class="text-lg font-bold text-orange-600">2</div>
                                        <div class="text-xs text-gray-500">需复习</div>
                                    </div>
                                    <div>
                                        <div class="text-lg font-bold text-blue-600">12</div>
                                        <div class="text-xs text-gray-500">总计</div>
                                    </div>
                                </div>
                                
                                <div class="mb-2">
                                    <div class="flex justify-between text-sm mb-1">
                                        <span class="text-gray-600">学习进度</span>
                                        <span class="text-green-600">100%</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 100%"></div>
                                    </div>
                                </div>
                                
                                <div class="text-xs text-gray-500">
                                    学习时长：15分钟
                                </div>
                            </div>
                            
                            <!-- 前天记录 -->
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center gap-2">
                                        <span class="font-bold text-gray-800">6月7日</span>
                                        <span class="text-sm text-gray-500">周五</span>
                                    </div>
                                    <span class="tag" style="background: #10B981; color: white;">已完成</span>
                                </div>
                                
                                <div class="grid grid-cols-3 gap-4 text-center mb-3">
                                    <div>
                                        <div class="text-lg font-bold text-green-600">8</div>
                                        <div class="text-xs text-gray-500">已掌握</div>
                                    </div>
                                    <div>
                                        <div class="text-lg font-bold text-orange-600">4</div>
                                        <div class="text-xs text-gray-500">需复习</div>
                                    </div>
                                    <div>
                                        <div class="text-lg font-bold text-blue-600">12</div>
                                        <div class="text-xs text-gray-500">总计</div>
                                    </div>
                                </div>
                                
                                <div class="mb-2">
                                    <div class="flex justify-between text-sm mb-1">
                                        <span class="text-gray-600">学习进度</span>
                                        <span class="text-green-600">100%</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 100%"></div>
                                    </div>
                                </div>
                                
                                <div class="text-xs text-gray-500">
                                    学习时长：18分钟
                                </div>
                            </div>
                            
                            <!-- 6月6日记录 -->
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center gap-2">
                                        <span class="font-bold text-gray-800">6月6日</span>
                                        <span class="text-sm text-gray-500">周四</span>
                                    </div>
                                    <span class="tag" style="background: #10B981; color: white;">已完成</span>
                                </div>
                                
                                <div class="grid grid-cols-3 gap-4 text-center mb-3">
                                    <div>
                                        <div class="text-lg font-bold text-green-600">9</div>
                                        <div class="text-xs text-gray-500">已掌握</div>
                                    </div>
                                    <div>
                                        <div class="text-lg font-bold text-orange-600">1</div>
                                        <div class="text-xs text-gray-500">需复习</div>
                                    </div>
                                    <div>
                                        <div class="text-lg font-bold text-blue-600">10</div>
                                        <div class="text-xs text-gray-500">总计</div>
                                    </div>
                                </div>
                                
                                <div class="mb-2">
                                    <div class="flex justify-between text-sm mb-1">
                                        <span class="text-gray-600">学习进度</span>
                                        <span class="text-green-600">100%</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 100%"></div>
                                    </div>
                                </div>
                                
                                <div class="text-xs text-gray-500">
                                    学习时长：12分钟
                                </div>
                            </div>
                        </div>
                        
                        <!-- 加载更多 -->
                        <button class="w-full mt-4 text-center text-green-600 font-medium py-2">
                            查看更多记录 <i class="fas fa-chevron-down ml-1"></i>
                        </button>
                    </div>
                </div>
                
                <!-- 分享按钮 -->
                <div class="px-4 pb-6">
                    <button class="btn-secondary w-full flex items-center justify-center gap-2">
                        <i class="fas fa-share"></i>
                        分享学习成果
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 时间筛选按钮切换
        document.querySelectorAll('.px-4 .btn-primary, .px-4 .btn-outline').forEach(btn => {
            btn.addEventListener('click', function() {
                // 移除所有active状态
                document.querySelectorAll('.px-4 .btn-primary, .px-4 .btn-outline').forEach(b => {
                    b.classList.remove('btn-primary');
                    b.classList.add('btn-outline');
                });
                
                // 添加active状态到当前按钮
                this.classList.remove('btn-outline');
                this.classList.add('btn-primary');
                
                // 按钮点击效果
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });
        
        // 记录卡片点击效果
        document.querySelectorAll('.border.rounded-lg').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                this.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                    this.style.boxShadow = '';
                }, 200);
            });
        });
        
        // 分享按钮点击
        document.querySelector('.btn-secondary').addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
                alert('分享功能演示：将生成学习成果图片');
            }, 150);
        });
        
        // 查看更多按钮
        document.querySelector('.text-green-600').addEventListener('click', function() {
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
        
        // 返回按钮
        document.querySelector('.nav-back').addEventListener('click', function() {
            this.style.transform = 'scale(0.9)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    </script>
</body>
</html>
