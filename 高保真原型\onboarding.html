<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新手引导 - 小柿子识字工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="common.css">
</head>
<body class="phone-screen">
            <!-- iOS状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </div>
            </div>
            
            <!-- 主内容区域 -->
            <div class="main-content">
                <!-- 引导内容 -->
                <div class="flex flex-col h-full">
                    <!-- 顶部图标和标题 -->
                    <div class="text-center pt-16 pb-8">
                        <div class="text-8xl mb-6">🍊</div>
                        <h1 class="text-3xl font-bold text-gray-800 mb-4">小柿子识字工具</h1>
                        <p class="text-lg text-gray-600 px-8">助力幼儿高效识字，让亲子学习更轻松</p>
                    </div>
                    
                    <!-- 核心功能介绍 -->
                    <div class="flex-1 px-6">
                        <div class="space-y-6">
                            <!-- 功能1 -->
                            <div class="card fade-in">
                                <div class="flex items-center gap-4">
                                    <div class="text-4xl">📚</div>
                                    <div>
                                        <h3 class="text-lg font-bold text-gray-800 mb-2">当日字词复习</h3>
                                        <p class="text-gray-600 text-sm">与幼儿园教学同步，快速复习当日新学字词</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 功能2 -->
                            <div class="card fade-in" style="animation-delay: 0.2s;">
                                <div class="flex items-center gap-4">
                                    <div class="text-4xl">🧠</div>
                                    <div>
                                        <h3 class="text-lg font-bold text-gray-800 mb-2">智能复习计划</h3>
                                        <p class="text-gray-600 text-sm">基于艾宾浩斯遗忘曲线，科学规划复习时间</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 功能3 -->
                            <div class="card fade-in" style="animation-delay: 0.4s;">
                                <div class="flex items-center gap-4">
                                    <div class="text-4xl">👨‍👩‍👧‍👦</div>
                                    <div>
                                        <h3 class="text-lg font-bold text-gray-800 mb-2">亲子互动学习</h3>
                                        <p class="text-gray-600 text-sm">家长陪伴指导，让学习变成快乐的亲子时光</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 特色说明 -->
                        <div class="mt-8 p-4 bg-green-50 rounded-lg border border-green-200">
                            <div class="flex items-center gap-2 mb-2">
                                <i class="fas fa-shield-alt text-green-600"></i>
                                <span class="font-medium text-green-800">本地存储，保护隐私</span>
                            </div>
                            <p class="text-green-700 text-sm">
                                所有数据仅保存在您的设备本地，无需联网，保障数据安全。
                                告别枯燥机械记忆，用最少的时间收获最佳学习效果。
                            </p>
                        </div>
                    </div>
                    
                    <!-- 底部按钮 -->
                    <div class="p-6">
                        <button class="btn-primary w-full text-lg py-4 slide-up">
                            我已了解，开始使用 🚀
                        </button>
                        
                        <!-- 操作提示 -->
                        <div class="mt-4 text-center">
                            <p class="text-gray-500 text-sm">
                                <i class="fas fa-mobile-alt mr-1"></i>
                                无需下载，微信内直接使用
                            </p>
                        </div>
                    </div>
                </div>
            </div>
    
    <script>
        // 添加点击事件模拟
        document.querySelector('.btn-primary').addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            this.style.background = '#6FA055';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
                this.style.background = 'var(--primary-green)';
            }, 150);
        });
        
        // 添加页面加载动画
        window.addEventListener('load', function() {
            const cards = document.querySelectorAll('.fade-in');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
