/**
 * 新手引导页面
 */

const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    currentStep: 0,
    steps: [
      {
        icon: '🍊',
        title: '小柿子识字工具',
        subtitle: '助力幼儿高效识字，让亲子学习更轻松',
        description: '本工具旨在帮助家长陪伴孩子高效复习所学字词，结合艾宾浩斯遗忘曲线科学规划学习，告别枯燥机械记忆，用最少的时间收获最佳学习效果。'
      },
      {
        icon: '📚',
        title: '当日字词复习',
        subtitle: '与幼儿园教学同步',
        description: '快速添加当日新学字词，通过大字显示和亲子互动，帮助幼儿巩固记忆。支持手动输入和批量导入，操作简单便捷。'
      },
      {
        icon: '🧠',
        title: '智能复习计划',
        subtitle: '基于艾宾浩斯遗忘曲线',
        description: '科学规划复习时间，在最佳记忆节点提醒复习，提高学习效率。系统自动生成个性化复习计划，无需家长操心。'
      },
      {
        icon: '👨‍👩‍👧‍👦',
        title: '亲子互动学习',
        subtitle: '让学习变成快乐时光',
        description: '提供科学的亲子互动指导，通过问答、游戏等方式，让识字学习变得有趣。家长陪伴指导，增进亲子关系。'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 检查是否需要显示引导页
    if (!app.globalData.isFirstLaunch) {
      this.navigateToHome();
    }
  },

  /**
   * 下一步
   */
  nextStep: function() {
    if (this.data.currentStep < this.data.steps.length - 1) {
      this.setData({
        currentStep: this.data.currentStep + 1
      });
    } else {
      this.startUsing();
    }
  },

  /**
   * 上一步
   */
  prevStep: function() {
    if (this.data.currentStep > 0) {
      this.setData({
        currentStep: this.data.currentStep - 1
      });
    }
  },

  /**
   * 跳过引导
   */
  skipGuide: function() {
    this.startUsing();
  },

  /**
   * 开始使用
   */
  startUsing: function() {
    // 标记引导已完成
    wx.setStorageSync('onboardingCompleted', true);
    
    // 检查是否已有用户数据
    const userInfo = app.dataManager.getUserInfo();
    if (!userInfo) {
      // 创建默认用户
      this.createDefaultUser();
    } else {
      this.navigateToHome();
    }
  },

  /**
   * 创建默认用户
   */
  createDefaultUser: function() {
    wx.showLoading({
      title: '初始化中...'
    });

    // 获取用户信息
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        // 使用微信用户信息创建用户
        const openId = 'wx_' + Date.now(); // 简化处理，实际应该获取真实openId
        app.dataManager.createUser(openId);
        
        wx.hideLoading();
        this.navigateToChildSetup();
      },
      fail: () => {
        // 用户拒绝授权，创建匿名用户
        const openId = 'anonymous_' + Date.now();
        app.dataManager.createUser(openId);
        
        wx.hideLoading();
        this.navigateToChildSetup();
      }
    });
  },

  /**
   * 跳转到幼儿信息设置
   */
  navigateToChildSetup: function() {
    wx.showModal({
      title: '设置幼儿信息',
      content: '为了提供更好的学习体验，请先设置幼儿的基本信息',
      showCancel: false,
      confirmText: '去设置',
      success: () => {
        wx.navigateTo({
          url: '/pages/child-management/child-management?action=add&fromOnboarding=true'
        });
      }
    });
  },

  /**
   * 跳转到首页
   */
  navigateToHome: function() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  },

  /**
   * 指示器点击
   */
  onIndicatorTap: function(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      currentStep: index
    });
  },

  /**
   * 滑动切换
   */
  onSwiperChange: function(e) {
    this.setData({
      currentStep: e.detail.current
    });
  }
});
