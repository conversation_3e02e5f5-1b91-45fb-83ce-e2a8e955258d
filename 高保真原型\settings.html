<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置 - 小柿子识字工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="common.css">
</head>
<body class="phone-screen">
            <!-- iOS状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </div>
            </div>
            
            <!-- 小程序导航栏 -->
            <div class="nav-header">
                <i class="nav-back fas fa-chevron-left"></i>
                <span>设置</span>
            </div>
            
            <!-- 主内容区域 -->
            <div class="main-content">
                <!-- 学习设置 -->
                <div class="p-4">
                    <div class="card">
                        <h3 class="text-lg font-bold text-gray-800 mb-4">📚 学习设置</h3>
                        
                        <div class="space-y-4">
                            <!-- 每次复习数量 -->
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="font-medium text-gray-800">每次复习数量</p>
                                    <p class="text-sm text-gray-500">建议10-15个字词，一天可复习多次</p>
                                </div>
                                <div class="flex items-center gap-2">
                                    <button class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center" id="decreaseBtn">
                                        <i class="fas fa-minus text-sm"></i>
                                    </button>
                                    <span class="w-8 text-center font-medium" id="reviewCount">12</span>
                                    <button class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center" id="increaseBtn">
                                        <i class="fas fa-plus text-sm"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 复习提醒 -->
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="font-medium text-gray-800">复习提醒</p>
                                    <p class="text-sm text-gray-500">每日定时提醒学习</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer" checked>
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-500"></div>
                                </label>
                            </div>
                            
                            <!-- 提醒时间 -->
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="font-medium text-gray-800">提醒时间</p>
                                    <p class="text-sm text-gray-500">设置学习提醒时间</p>
                                </div>
                                <button class="text-green-600 font-medium">
                                    19:00 <i class="fas fa-chevron-right ml-1"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 显示设置 -->
                <div class="px-4">
                    <div class="card">
                        <h3 class="text-lg font-bold text-gray-800 mb-4">🎨 显示设置</h3>
                        
                        <div class="space-y-3" style="list-style-type: none; padding-left: 0;">
                            <!-- 深色模式 -->
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-3">
                                    <div class="w-8 h-8 bg-gray-800 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-moon text-white"></i>
                                    </div>
                                    <p class="font-medium text-gray-800">深色模式</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" value="" class="sr-only peer" id="darkModeToggle">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                                </label>
                            </div>
                            
                            <!-- 字体大小 -->
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-3">
                                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-text-height text-blue-600"></i>
                                    </div>
                                    <p class="font-medium text-gray-800">字体大小</p>
                                </div>
                                <select class="bg-gray-100 border border-gray-300 text-gray-700 rounded-lg px-3 py-1.5 focus:outline-none">
                                    <option>小</option>
                                    <option selected>中</option>
                                    <option>大</option>
                                </select>
                            </div>
                            
                            <!-- 护眼模式 -->
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-3">
                                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-eye text-green-600"></i>
                                    </div>
                                    <p class="font-medium text-gray-800">护眼模式</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" value="" class="sr-only peer" checked id="eyeProtectionToggle">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 数据管理 -->
                <div class="px-4">
                    <div class="card">
                        <h3 class="text-lg font-bold text-gray-800 mb-4">💾 数据管理</h3>
                        
                        <div class="space-y-3" style="list-style-type: none; padding-left: 0;">
                            <!-- 清除缓存 -->
                            <div class="list-item">
                                <div class="flex items-center gap-3">
                                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-broom text-orange-600"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-800">清除缓存</p>
                                        <p class="text-xs text-gray-500">清理临时文件，释放存储空间</p>
                                    </div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                            
                            <!-- 重置数据 -->
                            <div class="list-item">
                                <div class="flex items-center gap-3">
                                    <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-trash text-red-600"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-800">重置所有数据</p>
                                        <p class="text-xs text-red-500">⚠️ 此操作不可恢复</p>
                                    </div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 关于应用 -->
                <div class="px-4">
                    <div class="card">
                        <h3 class="text-lg font-bold text-gray-800 mb-4">ℹ️ 关于应用</h3>
                        
                        <div class="space-y-3" style="list-style-type: none; padding-left: 0;">
                            <!-- 版本信息 -->
                            <div class="flex items-center justify-between">
                                <p class="font-medium text-gray-800">当前版本</p>
                                <span class="text-gray-600">v1.0.0</span>
                            </div>
                            
                            <!-- 意见反馈 -->
                            <div class="list-item">
                                <div class="flex items-center gap-3">
                                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                        💬
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-800">意见反馈</p>
                                        <p class="text-xs text-gray-500">关注公众号反馈问题</p>
                                    </div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 数据风险提示 -->
                <div class="px-4 pb-6">
                    <div class="p-4 bg-orange-50 rounded-lg border-l-4 border-orange-400">
                        <div class="flex items-start gap-2">
                            <i class="fas fa-exclamation-triangle text-orange-500 mt-1"></i>
                            <div>
                                <h4 class="font-medium text-orange-800 mb-1">⚠️ 数据存储提示</h4>
                                <p class="text-orange-700 text-sm leading-relaxed">
                                    本工具所有学习数据仅保存在您的设备本地，一旦删除本程序或设备故障导致数据丢失，无法通过任何方式找回。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    
    <script>
        let reviewCount = 12;
        
        // 复习数量调整
        document.getElementById('decreaseBtn').addEventListener('click', function() {
            if (reviewCount > 5) {
                reviewCount--;
                document.getElementById('reviewCount').textContent = reviewCount;
            }
            this.style.transform = 'scale(0.9)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
        
        document.getElementById('increaseBtn').addEventListener('click', function() {
            if (reviewCount < 20) {
                reviewCount++;
                document.getElementById('reviewCount').textContent = reviewCount;
            }
            this.style.transform = 'scale(0.9)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
        
        // 列表项点击效果
        document.querySelectorAll('.list-item').forEach(item => {
            item.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                this.style.background = 'var(--light-green)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                    this.style.background = 'white';
                }, 150);

                // 特殊处理重置数据
                if (this.textContent.includes('重置所有数据')) {
                    if (confirm('确定要重置所有数据吗？此操作不可恢复！')) {
                        alert('数据重置功能演示');
                    }
                }

                // 特殊处理意见反馈
                if (this.textContent.includes('意见反馈')) {
                    alert('请关注微信公众号"小柿子识字工具"进行意见反馈');
                }
            });
        });
        
        // 开关切换效果
        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                // 添加切换动画效果
                this.parentElement.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.parentElement.style.transform = 'scale(1)';
                }, 150);
            });
        });
        
        // 返回按钮
        document.querySelector('.nav-back').addEventListener('click', function() {
            this.style.transform = 'scale(0.9)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
        
        // 下拉选择框变化效果
        document.querySelector('select').addEventListener('change', function() {
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    </script>
</body>
</html>
