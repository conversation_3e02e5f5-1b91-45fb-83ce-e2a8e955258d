# 原型交互设计文档

## 一、原型结构说明

### 1.1 界面层级关系

```
小柿子识字工具
├── 新手引导页 (首次启动)
│   ├── 开发目的展示
│   ├── 操作指引
│   └── 确认按钮
├── 主界面 (Tab导航)
│   ├── 首页 (今日复习)
│   │   |── 快速添加字词
│   │   ├── 当日字词复习
│   │   └── 复习进度展示
│   ├── 复习 (历史复习)
│   │   ├── 艾宾浩斯复习计划
│   │   ├── 复习日历
│   │   └── 薄弱字词专项
│   └── 我的 (家长中心)
│       ├── 幼儿信息管理
│       ├── 字词库管理
│       ├── 学习记录
│       └── 设置页面
└── 功能页面
    ├── 字词复习详情页
    ├── 添加字词页
    ├── 学习记录详情页
    └── 设置页面
```

### 1.2 关键交互点

- **新手引导页**：滑动浏览 → 点击"我已了解"进入主界面
- **首页**：点击"添加字词" → 添加字词 → 和已有的字词进行匹配，发现新字词进入复习详情页 → 左右滑动切换字词 → 点击"已掌握/需复习"标记
- **复习页**：点击日历查看计划 → 选择复习任务 → 进入复习流程
- **我的页面**：点击各功能模块 → 进入对应设置页面
- **全局交互**：左滑返回、底部Tab切换、长按操作提示

## 二、交互逻辑文档

### 2.1 完整用户路径

#### 首次使用流程
```
启动小程序 → 新手引导页 → 点击"我已了解" 
→ 进入首页 → 弹窗提示设置幼儿信息 → 填写生日/班级 
→ 返回首页 → 点击"添加今日字词" → 手动输入或批量导入 
→ 开始复习 → 逐个标记字词 → 完成复习 → 查看学习记录
```

#### 日常使用流程
```
打开小程序 → 首页显示今日任务 → 点击"开始复习" 
→ 大字显示汉字 → 家长与幼儿讨论 → 标记掌握情况 
→ 自动跳转下一字词 → 完成所有字词 → 显示复习结果 
→ 自动生成艾宾浩斯复习计划 → 返回首页
```

```
打开小程序 → 首页显示“添加字词” → 点击"添加字词" → 和已有的字词进行匹配，发现新字词，开始复习→ 大字显示汉字 → 家长与幼儿讨论 → 标记掌握情况 
→ 自动跳转下一字词 → 完成所有字词 → 显示复习结果 
→ 自动生成艾宾浩斯复习计划 → 返回首页
```

### 2.2 艾宾浩斯算法界面呈现

#### 复习计划日历
- 月视图日历，清晰标记复习日期
- 不同颜色表示复习阶段：
  - 第1天：深绿色
  - 第2天：中绿色  
  - 第4天：浅绿色
  - 第7天：橙色
  - 第15天：红色

#### 进度条展示
- 字词掌握进度：圆形进度条，显示百分比
- 复习完成度：线性进度条，分段显示
- 薄弱字词统计：饼图展示，可点击查看详情

### 3.3 数据风险提示策略

#### 非干扰性提示
- 设置页面底部常驻显示，灰色小字
- 首次删除操作时弹窗提醒，可选择"不再提示"
- 关键操作前轻量级确认，不阻断主流程

#### 明显但不干扰
- 使用图标 + 文字组合，提高识别度
- 颜色使用警告橙色，但不过于突出
- 位置固定在页面底部，不占用主要操作区域

## 三、技术实现要点

### 3.1 微信小程序规范遵循
- 使用WeUI组件库，保持视觉一致性
- 遵循微信设计规范的颜色、字体、间距标准
- 适配不同屏幕尺寸，特别是iPhone 15 Pro (390x844px) 和 iPad Pro (1024x1366px)
- 支持微信原生的左滑返回手势

### 3.2 本地数据存储
- 使用wx.setStorageSync进行本地数据持久化
- 数据结构设计支持多用户、多字词库管理
- 实现数据导入导出功能，格式为JSON
- 定期清理过期数据，优化存储空间

### 3.3 性能优化
- 图片资源本地化，减少网络依赖
- 使用虚拟列表优化大量字词显示
- 实现懒加载，提升页面响应速度
- 合理使用缓存机制，提升用户体验

## 四、设计总结

### 4.1 设计亮点
1. **极简界面**：去除多余装饰，突出核心功能
2. **护眼配色**：低饱和度绿色系，保护幼儿视力
3. **大字显示**：楷体120pt，确保字词清晰可见
4. **智能提醒**：艾宾浩斯算法自动规划复习
5. **离线优先**：所有功能本地运行，无网络依赖

### 4.2 用户体验优化
1. **一步直达**：核心功能最多2步操作完成
2. **即时反馈**：操作后立即显示结果和进度
3. **容错设计**：重要操作有确认机制
4. **数据透明**：清晰提示数据存储方式和风险

### 4.3 技术特色
1. **微信原生**：完全遵循微信小程序设计规范
2. **本地存储**：使用微信小程序本地存储API
3. **性能优化**：虚拟列表、懒加载等技术
4. **兼容性强**：适配主流设备和微信版本
