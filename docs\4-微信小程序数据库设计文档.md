“小柿子・幼儿识字辅助工具” 微信小程序数据库设计文档



一、文档概述



### 1.1 文档目的&#xA;

本文档旨在为 “小柿子・幼儿识字辅助工具” 微信小程序的数据库设计提供全面、详细的技术方案，明确数据库的结构设计、数据存储方式、表间关系以及数据安全策略，为开发团队提供准确的开发依据，确保数据库能够高效、稳定地支撑小程序的各项功能需求。


### 1.2 适用范围&#xA;

本文档适用于参与 “小柿子・幼儿识字辅助工具” 微信小程序开发的数据库设计人员、开发工程师、测试人员以及相关项目管理人员，用于指导数据库的设计、开发、测试和维护工作。


### 1.3 参考资料&#xA;



*   《微信小程序开发文档》


*   《数据库设计规范》


二、设计原则



### 2.1 功能匹配原则&#xA;

数据库设计紧密围绕小程序的核心功能，包括用户管理、字词库管理、学习记录管理、复习计划管理，确保数据结构能够准确、高效地支持各功能模块的业务逻辑。


### 2.2 性能优化原则&#xA;

采用合理的数据结构和索引设计，减少数据冗余，提高数据查询、插入、更新和删除的效率，保证小程序在数据操作过程中的响应速度。


### 2.3 数据安全原则&#xA;

严格遵循数据安全规范，对用户敏感信息进行加密处理，限制数据访问权限，防止数据泄露和非法篡改，确保用户数据的安全性和隐私性。


### 2.4 扩展性原则&#xA;

考虑到小程序未来可能的功能扩展和业务增长，数据库设计具备良好的扩展性，能够方便地添加新的表、字段和功能模块，而不影响现有系统的正常运行。


三、需求分析



### 3.1 功能需求分析&#xA;

“小柿子・幼儿识字辅助工具” 微信小程序主要面向幼儿家长，提供幼儿识字学习与复习功能。具体功能需求如下：




*   **用户管理**：家长用户通过微信 OpenID 进行唯一标识，支持关联多个幼儿信息，包括幼儿姓名、生日、班级等。


*   **字词库管理**：仅存储家长添加的字词，字词需与具体幼儿绑定，记录字词的文本、掌握状态、复习次数和上次复习日期等信息。


*   **学习记录管理**：每日记录每个幼儿学习的字词数量、掌握数量以及薄弱字词列表，便于家长了解不同幼儿的学习进度和效果。


*   **复习计划管理**：基于艾宾浩斯遗忘曲线，为每个幼儿的字词制定科学的复习计划，包括复习日期和复习阶段。


### 3.2 数据需求分析&#xA;

根据功能需求，确定数据库需要存储以下数据：




*   **用户数据**：用户 ID（微信 OpenID）、幼儿信息列表（包含幼儿姓名、生日、班级、年龄、最后学习日期等）、用户创建时间。


*   **字词库数据**：字词 ID、幼儿 ID（关联具体幼儿）、字词文本、掌握状态、复习次数、上次复习日期。


*   **学习记录数据**：记录 ID、幼儿 ID、用户 ID、学习日期、学习字词数量、掌握字词数量、薄弱字词列表。


*   **复习计划数据**：计划 ID、幼儿 ID、用户 ID、字词 ID、复习日期、复习阶段。


四、数据库逻辑设计



### 4.1 数据模型设计&#xA;

采用关系型数据库模型，设计以下数据表：




*   **用户表（users）**：用于存储家长用户及关联的多个幼儿信息。
    \| 字段名称 | 数据类型 | 描述 | 是否主键 |
    \|---|---|---|---|
    |user\_id | 字符串 | 用户 ID（微信 OpenID），唯一标识用户 | 是 |
    |children\_info | 数组 | 幼儿信息列表，每个元素为包含幼儿姓名、生日、班级、年龄、最后学习日期等的对象 | 否 |
    |create\_time | 字符串 | 用户创建时间，格式为 YYYY-MM-DD HH:mm:ss | 否 |


    \| 字段名称 | 数据类型 | 描述 | 是否主键 |
    \|---|---|---|---|
    |user\_id | 字符串 | 用户 ID（微信 OpenID），唯一标识用户 | 是 |
    |children\_info | 数组 | 幼儿信息列表，每个元素为包含幼儿姓名、生日、班级、年龄、最后学习日期等的对象 | 否 |
    |create\_time | 字符串 | 用户创建时间，格式为 YYYY-MM-DD HH:mm:ss | 否 |


    \|---|---|---|---|
    |user\_id | 字符串 | 用户 ID（微信 OpenID），唯一标识用户 | 是 |
    |children\_info | 数组 | 幼儿信息列表，每个元素为包含幼儿姓名、生日、班级、年龄、最后学习日期等的对象 | 否 |
    |create\_time | 字符串 | 用户创建时间，格式为 YYYY-MM-DD HH:mm:ss | 否 |


    |user\_id | 字符串 | 用户 ID（微信 OpenID），唯一标识用户 | 是 |
    |children\_info | 数组 | 幼儿信息列表，每个元素为包含幼儿姓名、生日、班级、年龄、最后学习日期等的对象 | 否 |
    |create\_time | 字符串 | 用户创建时间，格式为 YYYY-MM-DD HH:mm:ss | 否 |


    |children\_info | 数组 | 幼儿信息列表，每个元素为包含幼儿姓名、生日、班级、年龄、最后学习日期等的对象 | 否 |
    |create\_time | 字符串 | 用户创建时间，格式为 YYYY-MM-DD HH:mm:ss | 否 |


    |create\_time | 字符串 | 用户创建时间，格式为 YYYY-MM-DD HH:mm:ss | 否 |


*   **字词库表（word\_library）**：用于存储与幼儿绑定的字词相关信息。
    \| 字段名称 | 数据类型 | 描述 | 是否主键 |
    \|---|---|---|---|
    |word\_id | 字符串 | 字词 ID，通过 UUID 生成，唯一标识每个字词 | 是 |
    |child\_id | 字符串 | 幼儿 ID，关联用户表中幼儿信息，确定字词所属幼儿 | 否 |
    |word\_text | 字符串 | 字词文本 | 否 |
    |mastered\_status | 布尔值 | 字词掌握状态，true 表示已掌握，false 表示未掌握 | 否 |
    |review\_times | 数字 | 字词累计复习次数 | 否 |
    |last\_review\_date | 字符串 | 上次复习日期，格式为 YYYY-MM-DD | 否 |


    \| 字段名称 | 数据类型 | 描述 | 是否主键 |
    \|---|---|---|---|
    |word\_id | 字符串 | 字词 ID，通过 UUID 生成，唯一标识每个字词 | 是 |
    |child\_id | 字符串 | 幼儿 ID，关联用户表中幼儿信息，确定字词所属幼儿 | 否 |
    |word\_text | 字符串 | 字词文本 | 否 |
    |mastered\_status | 布尔值 | 字词掌握状态，true 表示已掌握，false 表示未掌握 | 否 |
    |review\_times | 数字 | 字词累计复习次数 | 否 |
    |last\_review\_date | 字符串 | 上次复习日期，格式为 YYYY-MM-DD | 否 |


    \|---|---|---|---|
    |word\_id | 字符串 | 字词 ID，通过 UUID 生成，唯一标识每个字词 | 是 |
    |child\_id | 字符串 | 幼儿 ID，关联用户表中幼儿信息，确定字词所属幼儿 | 否 |
    |word\_text | 字符串 | 字词文本 | 否 |
    |mastered\_status | 布尔值 | 字词掌握状态，true 表示已掌握，false 表示未掌握 | 否 |
    |review\_times | 数字 | 字词累计复习次数 | 否 |
    |last\_review\_date | 字符串 | 上次复习日期，格式为 YYYY-MM-DD | 否 |


    |word\_id | 字符串 | 字词 ID，通过 UUID 生成，唯一标识每个字词 | 是 |
    |child\_id | 字符串 | 幼儿 ID，关联用户表中幼儿信息，确定字词所属幼儿 | 否 |
    |word\_text | 字符串 | 字词文本 | 否 |
    |mastered\_status | 布尔值 | 字词掌握状态，true 表示已掌握，false 表示未掌握 | 否 |
    |review\_times | 数字 | 字词累计复习次数 | 否 |
    |last\_review\_date | 字符串 | 上次复习日期，格式为 YYYY-MM-DD | 否 |


    |child\_id | 字符串 | 幼儿 ID，关联用户表中幼儿信息，确定字词所属幼儿 | 否 |
    |word\_text | 字符串 | 字词文本 | 否 |
    |mastered\_status | 布尔值 | 字词掌握状态，true 表示已掌握，false 表示未掌握 | 否 |
    |review\_times | 数字 | 字词累计复习次数 | 否 |
    |last\_review\_date | 字符串 | 上次复习日期，格式为 YYYY-MM-DD | 否 |


    |word\_text | 字符串 | 字词文本 | 否 |
    |mastered\_status | 布尔值 | 字词掌握状态，true 表示已掌握，false 表示未掌握 | 否 |
    |review\_times | 数字 | 字词累计复习次数 | 否 |
    |last\_review\_date | 字符串 | 上次复习日期，格式为 YYYY-MM-DD | 否 |


    |mastered\_status | 布尔值 | 字词掌握状态，true 表示已掌握，false 表示未掌握 | 否 |
    |review\_times | 数字 | 字词累计复习次数 | 否 |
    |last\_review\_date | 字符串 | 上次复习日期，格式为 YYYY-MM-DD | 否 |


    |review\_times | 数字 | 字词累计复习次数 | 否 |
    |last\_review\_date | 字符串 | 上次复习日期，格式为 YYYY-MM-DD | 否 |


    |last\_review\_date | 字符串 | 上次复习日期，格式为 YYYY-MM-DD | 否 |


*   **学习记录表（learning\_records）**：用于记录每个幼儿每日学习情况及复习结果。
    \| 字段名称 | 数据类型 | 描述 | 是否主键 |
    \|---|---|---|---|
    |record\_id | 字符串 | 记录 ID，通过 UUID 生成，唯一标识每条学习记录 | 是 |
    |child\_id | 字符串 | 幼儿 ID，关联用户表中幼儿信息，标识学习记录所属幼儿 | 否 |
    |user\_id | 字符串 | 用户 ID，关联用户表 user\_id，确定记录所属家长用户 | 否 |
    |learn\_date | 字符串 | 学习日期，格式为 YYYY-MM-DD | 否 |
    |word\_count | 数字 | 当日学习的字词总数 | 否 |
    |mastered\_count | 数字 | 当日成功掌握的字词数量 | 否 |
    |weak\_words | 数组 | 当日学习过程中幼儿掌握薄弱的字词列表 | 否 |


    \| 字段名称 | 数据类型 | 描述 | 是否主键 |
    \|---|---|---|---|
    |record\_id | 字符串 | 记录 ID，通过 UUID 生成，唯一标识每条学习记录 | 是 |
    |child\_id | 字符串 | 幼儿 ID，关联用户表中幼儿信息，标识学习记录所属幼儿 | 否 |
    |user\_id | 字符串 | 用户 ID，关联用户表 user\_id，确定记录所属家长用户 | 否 |
    |learn\_date | 字符串 | 学习日期，格式为 YYYY-MM-DD | 否 |
    |word\_count | 数字 | 当日学习的字词总数 | 否 |
    |mastered\_count | 数字 | 当日成功掌握的字词数量 | 否 |
    |weak\_words | 数组 | 当日学习过程中幼儿掌握薄弱的字词列表 | 否 |


    \|---|---|---|---|
    |record\_id | 字符串 | 记录 ID，通过 UUID 生成，唯一标识每条学习记录 | 是 |
    |child\_id | 字符串 | 幼儿 ID，关联用户表中幼儿信息，标识学习记录所属幼儿 | 否 |
    |user\_id | 字符串 | 用户 ID，关联用户表 user\_id，确定记录所属家长用户 | 否 |
    |learn\_date | 字符串 | 学习日期，格式为 YYYY-MM-DD | 否 |
    |word\_count | 数字 | 当日学习的字词总数 | 否 |
    |mastered\_count | 数字 | 当日成功掌握的字词数量 | 否 |
    |weak\_words | 数组 | 当日学习过程中幼儿掌握薄弱的字词列表 | 否 |


    |record\_id | 字符串 | 记录 ID，通过 UUID 生成，唯一标识每条学习记录 | 是 |
    |child\_id | 字符串 | 幼儿 ID，关联用户表中幼儿信息，标识学习记录所属幼儿 | 否 |
    |user\_id | 字符串 | 用户 ID，关联用户表 user\_id，确定记录所属家长用户 | 否 |
    |learn\_date | 字符串 | 学习日期，格式为 YYYY-MM-DD | 否 |
    |word\_count | 数字 | 当日学习的字词总数 | 否 |
    |mastered\_count | 数字 | 当日成功掌握的字词数量 | 否 |
    |weak\_words | 数组 | 当日学习过程中幼儿掌握薄弱的字词列表 | 否 |


    |child\_id | 字符串 | 幼儿 ID，关联用户表中幼儿信息，标识学习记录所属幼儿 | 否 |
    |user\_id | 字符串 | 用户 ID，关联用户表 user\_id，确定记录所属家长用户 | 否 |
    |learn\_date | 字符串 | 学习日期，格式为 YYYY-MM-DD | 否 |
    |word\_count | 数字 | 当日学习的字词总数 | 否 |
    |mastered\_count | 数字 | 当日成功掌握的字词数量 | 否 |
    |weak\_words | 数组 | 当日学习过程中幼儿掌握薄弱的字词列表 | 否 |


    |user\_id | 字符串 | 用户 ID，关联用户表 user\_id，确定记录所属家长用户 | 否 |
    |learn\_date | 字符串 | 学习日期，格式为 YYYY-MM-DD | 否 |
    |word\_count | 数字 | 当日学习的字词总数 | 否 |
    |mastered\_count | 数字 | 当日成功掌握的字词数量 | 否 |
    |weak\_words | 数组 | 当日学习过程中幼儿掌握薄弱的字词列表 | 否 |


    |learn\_date | 字符串 | 学习日期，格式为 YYYY-MM-DD | 否 |
    |word\_count | 数字 | 当日学习的字词总数 | 否 |
    |mastered\_count | 数字 | 当日成功掌握的字词数量 | 否 |
    |weak\_words | 数组 | 当日学习过程中幼儿掌握薄弱的字词列表 | 否 |


    |word\_count | 数字 | 当日学习的字词总数 | 否 |
    |mastered\_count | 数字 | 当日成功掌握的字词数量 | 否 |
    |weak\_words | 数组 | 当日学习过程中幼儿掌握薄弱的字词列表 | 否 |


    |mastered\_count | 数字 | 当日成功掌握的字词数量 | 否 |
    |weak\_words | 数组 | 当日学习过程中幼儿掌握薄弱的字词列表 | 否 |


    |weak\_words | 数组 | 当日学习过程中幼儿掌握薄弱的字词列表 | 否 |


*   **复习计划表（review\_plans）**：用于存储基于艾宾浩斯曲线，为每个幼儿的字词制定的复习任务计划。
    \| 字段名称 | 数据类型 | 描述 | 是否主键 |
    \|---|---|---|---|
    |plan\_id | 字符串 | 计划 ID，通过 UUID 生成，唯一标识每个复习计划 | 是 |
    |child\_id | 字符串 | 幼儿 ID，关联用户表中幼儿信息，确定复习计划归属幼儿 | 否 |
    |user\_id | 字符串 | 用户 ID，关联用户表 user\_id，确定复习计划归属家长用户 | 否 |
    |word\_id | 字符串 | 字词 ID，关联字词库表 word\_id，明确需复习的具体字词 | 否 |
    |review\_date | 字符串 | 计划复习日期，格式为 YYYY-MM-DD | 否 |
    |review\_stage | 数字 | 复习阶段，1 - 5 阶段对应不同遗忘曲线周期 | 否 |


    \| 字段名称 | 数据类型 | 描述 | 是否主键 |
    \|---|---|---|---|
    |plan\_id | 字符串 | 计划 ID，通过 UUID 生成，唯一标识每个复习计划 | 是 |
    |child\_id | 字符串 | 幼儿 ID，关联用户表中幼儿信息，确定复习计划归属幼儿 | 否 |
    |user\_id | 字符串 | 用户 ID，关联用户表 user\_id，确定复习计划归属家长用户 | 否 |
    |word\_id | 字符串 | 字词 ID，关联字词库表 word\_id，明确需复习的具体字词 | 否 |
    |review\_date | 字符串 | 计划复习日期，格式为 YYYY-MM-DD | 否 |
    |review\_stage | 数字 | 复习阶段，1 - 5 阶段对应不同遗忘曲线周期 | 否 |


    \|---|---|---|---|
    |plan\_id | 字符串 | 计划 ID，通过 UUID 生成，唯一标识每个复习计划 | 是 |
    |child\_id | 字符串 | 幼儿 ID，关联用户表中幼儿信息，确定复习计划归属幼儿 | 否 |
    |user\_id | 字符串 | 用户 ID，关联用户表 user\_id，确定复习计划归属家长用户 | 否 |
    |word\_id | 字符串 | 字词 ID，关联字词库表 word\_id，明确需复习的具体字词 | 否 |
    |review\_date | 字符串 | 计划复习日期，格式为 YYYY-MM-DD | 否 |
    |review\_stage | 数字 | 复习阶段，1 - 5 阶段对应不同遗忘曲线周期 | 否 |


    |plan\_id | 字符串 | 计划 ID，通过 UUID 生成，唯一标识每个复习计划 | 是 |
    |child\_id | 字符串 | 幼儿 ID，关联用户表中幼儿信息，确定复习计划归属幼儿 | 否 |
    |user\_id | 字符串 | 用户 ID，关联用户表 user\_id，确定复习计划归属家长用户 | 否 |
    |word\_id | 字符串 | 字词 ID，关联字词库表 word\_id，明确需复习的具体字词 | 否 |
    |review\_date | 字符串 | 计划复习日期，格式为 YYYY-MM-DD | 否 |
    |review\_stage | 数字 | 复习阶段，1 - 5 阶段对应不同遗忘曲线周期 | 否 |


    |child\_id | 字符串 | 幼儿 ID，关联用户表中幼儿信息，确定复习计划归属幼儿 | 否 |
    |user\_id | 字符串 | 用户 ID，关联用户表 user\_id，确定复习计划归属家长用户 | 否 |
    |word\_id | 字符串 | 字词 ID，关联字词库表 word\_id，明确需复习的具体字词 | 否 |
    |review\_date | 字符串 | 计划复习日期，格式为 YYYY-MM-DD | 否 |
    |review\_stage | 数字 | 复习阶段，1 - 5 阶段对应不同遗忘曲线周期 | 否 |


    |user\_id | 字符串 | 用户 ID，关联用户表 user\_id，确定复习计划归属家长用户 | 否 |
    |word\_id | 字符串 | 字词 ID，关联字词库表 word\_id，明确需复习的具体字词 | 否 |
    |review\_date | 字符串 | 计划复习日期，格式为 YYYY-MM-DD | 否 |
    |review\_stage | 数字 | 复习阶段，1 - 5 阶段对应不同遗忘曲线周期 | 否 |


    |word\_id | 字符串 | 字词 ID，关联字词库表 word\_id，明确需复习的具体字词 | 否 |
    |review\_date | 字符串 | 计划复习日期，格式为 YYYY-MM-DD | 否 |
    |review\_stage | 数字 | 复习阶段，1 - 5 阶段对应不同遗忘曲线周期 | 否 |


    |review\_date | 字符串 | 计划复习日期，格式为 YYYY-MM-DD | 否 |
    |review\_stage | 数字 | 复习阶段，1 - 5 阶段对应不同遗忘曲线周期 | 否 |


    |review\_stage | 数字 | 复习阶段，1 - 5 阶段对应不同遗忘曲线周期 | 否 |


### 4.2 表间关系设计&#xA;



*   **用户表与字词库表**：通过 user\_id 和 child\_id 建立一对多关系，即一个用户的多个幼儿可以拥有各自的字词库。


*   **用户表与学习记录表**：通过 user\_id 和 child\_id 建立一对多关系，一个用户的多个幼儿对应多条学习记录。


*   **用户表与复习计划表**：通过 user\_id 和 child\_id 建立一对多关系，一个用户的多个幼儿有多条复习计划。


*   **字词库表与复习计划表**：通过 word\_id 和 child\_id 建立一对一关系，每个幼儿的每个字词对应唯一的复习计划。


*   **学习记录表与字词库表**：通过 child\_id 建立关联，可统计每个幼儿在字词库中的学习情况。


五、数据库物理设计



### 5.1 存储引擎选择&#xA;

采用微信小程序本地存储（wx.setStorageSync API）作为数据存储引擎，符合小程序离线使用的特性，能够满足数据存储和读取的基本需求。


### 5.2 数据文件设计&#xA;

所有数据统一存储在名为 “app\_data.json” 的文件中，便于管理和维护。同时，限制本地存储最大容量为 10MB，避免数据过度膨胀影响小程序性能。


### 5.3 索引设计&#xA;

根据数据查询需求，在以下字段上创建索引，提高查询效率：




*   用户表：user\_id


*   字词库表：word\_id、child\_id、user\_id、mastered\_status


*   学习记录表：child\_id、user\_id、learn\_date


*   复习计划表：child\_id、user\_id、word\_id、review\_date


六、数据存储规范



### 6.1 数据存储方式&#xA;

使用微信小程序提供的本地存储 API（wx.setStorageSync）进行数据的存储和读取操作。数据以 JSON 格式进行组织和存储，确保数据的可读性和可扩展性。


### 6.2 数据安全策略&#xA;



*   **数据加密**：对用户敏感信息，如幼儿生日，采用 AES - 128 加密算法进行加密处理，防止数据在存储和传输过程中被窃取。


*   **访问控制**：严格限制数据访问权限，仅允许家长用户进行读写操作，禁止幼儿独立修改数据。通过用户身份验证和权限管理机制，确保只有合法用户才能访问和操作相关数据。同时，家长用户只能访问和管理自己关联幼儿的数据。


*   **备份策略**：不提供自动备份机制，要求用户定期手动导出数据进行备份，避免因设备故障或数据丢失导致用户数据无法恢复。在小程序的设置页面中，以显著方式提示用户数据存储风险及备份重要性。


七、模拟数据



为便于理解数据库的实际运行逻辑，提供以下模拟数据示例：




*   **用户表**



```
{


&#x20; "user\_id": "wx\_user123",


&#x20; "children\_info": \[


&#x20;   {


&#x20;     "child\_id": "child\_001",


&#x20;     "child\_name": "小明",


&#x20;     "birthday": "2020-03",


&#x20;     "class\_type": "中班",


&#x20;     "age": 5,


&#x20;     "last\_learn\_date": "2025-06-05"


&#x20;   },


&#x20;   {


&#x20;     "child\_id": "child\_002",


&#x20;     "child\_name": "小红",


&#x20;     "birthday": "2021-05",


&#x20;     "class\_type": "小班",


&#x20;     "age": 4,


&#x20;     "last\_learn\_date": "2025-06-04"


&#x20;   }


&#x20; ],


&#x20; "create\_time": "2025-05-20 14:30:00"


}
```



*   **字词库表**



```
\[


&#x20; {


&#x20;   "word\_id": "word\_001",


&#x20;   "child\_id": "child\_001",


&#x20;   "word\_text": "日",


&#x20;   "mastered\_status": true,


&#x20;   "review\_times": 3,


&#x20;   "last\_review\_date": "2025-06-04"


&#x20; },


&#x20; {


&#x20;   "word\_id": "word\_002",


&#x20;   "child\_id": "child\_001",


&#x20;   "word\_text": "月",


&#x20;   "mastered\_status": false,


&#x20;   "review\_times": 1,


&#x20;   "last\_review\_date": "2025-06-05"


&#x20; },


&#x20; {


&#x20;   "word\_id": "word\_003",


&#x20;   "child\_id": "child\_002",


&#x20;   "word\_text": "水",


&#x20;   "mastered\_status": false,


&#x20;   "review\_times": 0,


&#x20;   "last\_review\_date": ""


&#x20; }


]
```



*   **学习记录表**



```
\[


&#x20; {


&#x20;   "record\_id": "record\_20250605\_001",


&#x20;   "child\_id": "child\_001",


&#x20;   "user\_id": "wx\_user123",


&#x20;   "learn\_date": "2025-06-05",


&#x20;   "word\_count": 5,


&#x20;   "mastered\_count": 3,


&#x20;   "weak\_words": \["月", "水"]


&#x20; },


&#x20; {


&#x20;   "record\_id": "record\_20250604\_002",


&#x20;   "child\_id": "child\_002",


&#x20;   "user\_id": "wx\_user123",


&#x20;   "learn\_date": "2025-06-04",


&#x20;   "word\_count": 3,


&#x20;   "mastered\_count": 1,


&#x20;   "weak\_words": \["水", "火"]


&#x20; }


]
```



*   **复习计划表**



```
\[


&#x20; {


&#x20;   "plan\_id": "plan\_20250607\_001",


&#x20;   "child\_id": "child\_001",


&#x20;   "user\_id": "wx\_user123",


&#x20;   "word\_id": "word\_001",


&#x20;   "review\_date": "2025-06-07",


&#x20;   "review\_stage": 3


&#x20; },


&#x20; {


&#x20;   "plan\_id": "plan\_20250606\_002",


&#x20;   "child\_id": "child\_002",


&#x20;   "user\_id": "wx\_user123",


&#x20;   "word\_id": "word\_003",


&#x20;   "review\_date": "2025-06-06",


&#x20;   "review\_stage": 1


&#x20; }

]
```

上述模拟数据展示了用户 “wx\_user123” 下两名幼儿 “小明” 和 “小红” 的学习情况，以及各自字词的复习计划安排，直观呈现了数据库各表之间的数据关联和实际运行逻辑。


八、附录



### 8.1 术语解释&#xA;



*   **微信 OpenID**：微信为每个小程序用户分配的唯一标识，用于区分不同用户。


*   **UUID**：通用唯一识别码，用于生成唯一的标识符，确保数据库中记录的唯一性。


*   **AES - 128**：一种对称加密算法，使用 128 位密钥对数据进行加密和解密，提供较高的数据安全性。


### 8.2 修订记录&#xA;



| 修订日期&#xA;             | 修订内容&#xA;                       | 修订人&#xA;   |
| --------------------- | ------------------------------- | ---------- |
| 2025 年 06 月 06 日&#xA; | 创建文档，完成数据库设计初稿&#xA;             | \[姓名]&#xA; |
| 2025 年 06 月 06 日&#xA; | 根据需求增加多幼儿管理相关设计，修改表结构、表间关系及模拟数据 | \[姓名]&#xA; |

> （注：文档部分内容可能由 AI 生成）
>