/**
 * 小柿子·幼儿识字辅助工具
 * 全局样式文件
 */

/* 自定义护眼色调变量 */
page {
  --primary-green: #7FB069;
  --light-green: #B8E6B8;
  --soft-green: #E8F5E8;
  --soft-orange: #F4A261;
  --warm-beige: #F7F3E9;
  --text-dark: #2D3748;
  --text-light: #718096;
  --border-light: #E2E8F0;
  --shadow-soft: 0 4px 12px rgba(0,0,0,0.1);
  
  background-color: var(--warm-beige);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* 通用容器 */
.container {
  padding: 32rpx;
  background-color: var(--warm-beige);
  min-height: 100vh;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 32rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: var(--shadow-soft);
  border: 1px solid var(--border-light);
}

.card-title {
  font-size: 34rpx;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

/* 按钮样式 */
.btn-primary {
  background: var(--primary-green);
  color: white;
  border: none;
  border-radius: 24rpx;
  padding: 24rpx 48rpx;
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
  box-shadow: var(--shadow-soft);
  transition: all 0.3s ease;
}

.btn-primary:active {
  background: #6FA055;
  transform: scale(0.95);
}

.btn-secondary {
  background: var(--soft-orange);
  color: white;
  border: none;
  border-radius: 24rpx;
  padding: 24rpx 48rpx;
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
  box-shadow: var(--shadow-soft);
  transition: all 0.3s ease;
}

.btn-secondary:active {
  background: #E8935A;
  transform: scale(0.95);
}

.btn-outline {
  background: transparent;
  color: var(--primary-green);
  border: 4rpx solid var(--primary-green);
  border-radius: 24rpx;
  padding: 20rpx 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
}

.btn-outline:active {
  background: var(--primary-green);
  color: white;
}

/* 大字显示样式 */
.big-character {
  font-family: 'KaiTi', '楷体', serif;
  font-size: 240rpx;
  font-weight: bold;
  color: var(--text-dark);
  text-align: center;
  line-height: 1.2;
  margin: 80rpx 0;
}

/* 进度条样式 */
.progress-bar {
  width: 100%;
  height: 16rpx;
  background: var(--soft-green);
  border-radius: 8rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--primary-green);
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

/* 输入框样式 */
.input-field {
  width: 100%;
  padding: 24rpx 32rpx;
  border: 4rpx solid var(--border-light);
  border-radius: 24rpx;
  font-size: 32rpx;
  background: white;
  box-sizing: border-box;
}

.input-field:focus {
  border-color: var(--primary-green);
}

/* 列表项样式 */
.list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1px solid var(--border-light);
  background-color: white;
  border-radius: 16rpx;
  margin-bottom: 2rpx;
  position: relative;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item-icon {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  font-size: 48rpx;
}

.list-item-content {
  flex: 1;
  min-width: 0;
}

.list-item-title {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-dark);
  margin-bottom: 8rpx;
}

.list-item-desc {
  font-size: 24rpx;
  color: var(--text-light);
}

.list-item-arrow {
  font-size: 28rpx;
  color: var(--text-light);
}

/* 标签样式 */
.tag {
  display: inline-block;
  padding: 8rpx 24rpx;
  background: var(--light-green);
  color: var(--text-dark);
  border-radius: 40rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.tag-orange {
  background: var(--soft-orange);
  color: white;
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.font-bold { font-weight: bold; }
.font-medium { font-weight: 500; }
.mb-4 { margin-bottom: 32rpx; }
.mt-4 { margin-top: 32rpx; }
.p-4 { padding: 32rpx; }
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.gap-4 { gap: 32rpx; }
.w-full { width: 100%; }
.h-full { height: 100%; }

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(40rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}
