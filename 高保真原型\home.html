<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页 - 小柿子识字工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="common.css">
</head>
<body class="phone-screen">
            <!-- iOS状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </div>
            </div>
            
            <!-- 小程序导航栏 -->
            <div class="nav-header">
                <span>🍊 小柿子识字</span>
            </div>
            
            <!-- 主内容区域 -->
            <div class="main-content">
                <!-- 欢迎区域 -->
                <div class="p-6 bg-gradient-to-r from-green-400 to-green-500 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-xl font-bold mb-1">早上好！👋</h2>
                            <p class="text-green-100">今天也要加油学习哦</p>
                        </div>
                        <div class="text-right">
                            <div class="text-2xl mb-1">🌞</div>
                            <p class="text-sm text-green-100">小明 · 中班</p>
                        </div>
                    </div>
                </div>
                
                <!-- 今日任务卡片 -->
                <div class="p-4">
                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-bold text-gray-800">📅 今日任务</h3>
                            <span class="tag">6月9日</span>
                        </div>
                        
                        <!-- 进度显示 -->
                        <div class="mb-4">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm text-gray-600">今日复习进度</span>
                                <span class="text-sm font-medium text-green-600">3/8 完成</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 37.5%"></div>
                            </div>
                        </div>
                        
                        <!-- 快速操作按钮 -->
                        <div class="grid grid-cols-2 gap-3">
                            <button class="btn-primary flex items-center justify-center gap-2 py-3">
                                <i class="fas fa-plus"></i>
                                添加字词
                            </button>
                            <button class="btn-secondary flex items-center justify-center gap-2 py-3">
                                <i class="fas fa-play"></i>
                                开始复习
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 今日字词列表 -->
                <div class="px-4">
                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-bold text-gray-800">📝 今日字词</h3>
                            <span class="text-sm text-gray-500">8个字词</span>
                        </div>
                        
                        <div class="space-y-3">
                            <!-- 字词项 -->
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center gap-3">
                                    <div class="text-2xl font-bold text-gray-800">太</div>
                                    <div>
                                        <p class="font-medium text-gray-800">太阳</p>
                                        <p class="text-xs text-gray-500">已复习</p>
                                    </div>
                                </div>
                                <i class="fas fa-check-circle text-green-500"></i>
                            </div>
                            
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center gap-3">
                                    <div class="text-2xl font-bold text-gray-800">阳</div>
                                    <div>
                                        <p class="font-medium text-gray-800">阳光</p>
                                        <p class="text-xs text-gray-500">已复习</p>
                                    </div>
                                </div>
                                <i class="fas fa-check-circle text-green-500"></i>
                            </div>
                            
                            <div class="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                                <div class="flex items-center gap-3">
                                    <div class="text-2xl font-bold text-gray-800">花</div>
                                    <div>
                                        <p class="font-medium text-gray-800">花朵</p>
                                        <p class="text-xs text-orange-600">待复习</p>
                                    </div>
                                </div>
                                <i class="fas fa-clock text-orange-500"></i>
                            </div>
                            
                            <div class="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                                <div class="flex items-center gap-3">
                                    <div class="text-2xl font-bold text-gray-800">草</div>
                                    <div>
                                        <p class="font-medium text-gray-800">小草</p>
                                        <p class="text-xs text-orange-600">待复习</p>
                                    </div>
                                </div>
                                <i class="fas fa-clock text-orange-500"></i>
                            </div>
                        </div>
                        
                        <button class="w-full mt-4 text-center text-green-600 font-medium py-2">
                            查看全部 8 个字词 <i class="fas fa-chevron-right ml-1"></i>
                        </button>
                    </div>
                </div>
                
                <!-- 学习统计 -->
                <div class="px-4 pb-6">
                    <div class="card">
                        <h3 class="text-lg font-bold text-gray-800 mb-4">📊 本周学习</h3>
                        <div class="grid grid-cols-3 gap-4 text-center">
                            <div>
                                <div class="text-2xl font-bold text-green-600">24</div>
                                <div class="text-xs text-gray-500">已学字词</div>
                            </div>
                            <div>
                                <div class="text-2xl font-bold text-orange-500">5</div>
                                <div class="text-xs text-gray-500">待复习</div>
                            </div>
                            <div>
                                <div class="text-2xl font-bold text-blue-500">6</div>
                                <div class="text-xs text-gray-500">学习天数</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 底部Tab导航 -->
            <div class="tab-bar">
                <div class="tab-item active">
                    <div class="tab-icon">🏠</div>
                    <span>首页</span>
                </div>
                <div class="tab-item">
                    <div class="tab-icon">📚</div>
                    <span>复习</span>
                </div>
                <div class="tab-item">
                    <div class="tab-icon">👤</div>
                    <span>我的</span>
                </div>
            </div>
    
    <script>
        // 添加按钮点击效果
        document.querySelectorAll('button').forEach(button => {
            button.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });
        
        // Tab切换效果
        document.querySelectorAll('.tab-item').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.tab-item').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>
