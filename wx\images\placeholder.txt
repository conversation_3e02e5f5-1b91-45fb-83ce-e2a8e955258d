# 图标文件说明

由于无法直接生成图片文件，请按以下步骤添加tabBar图标：

## 方案一：使用在线图标生成器
1. 访问 https://www.iconfont.cn/ 或其他图标网站
2. 搜索并下载以下图标（建议尺寸：81x81px）：
   - home.png (首页图标)
   - home-active.png (首页选中图标)
   - review.png (复习图标)
   - review-active.png (复习选中图标)
   - profile.png (我的图标)
   - profile-active.png (我的选中图标)

3. 将图标文件放置在以下路径：
   - wx/images/tab/home.png
   - wx/images/tab/home-active.png
   - wx/images/tab/review.png
   - wx/images/tab/review-active.png
   - wx/images/tab/profile.png
   - wx/images/tab/profile-active.png

## 方案二：修改app.json使用图标
如果有了图标文件，请修改 wx/app.json 中的 tabBar 配置：

```json
"tabBar": {
  "color": "#718096",
  "selectedColor": "#7FB069",
  "backgroundColor": "#ffffff",
  "borderStyle": "black",
  "list": [
    {
      "pagePath": "pages/home/<USER>",
      "text": "首页",
      "iconPath": "images/tab/home.png",
      "selectedIconPath": "images/tab/home-active.png"
    },
    {
      "pagePath": "pages/review/review",
      "text": "复习",
      "iconPath": "images/tab/review.png",
      "selectedIconPath": "images/tab/review-active.png"
    },
    {
      "pagePath": "pages/profile/profile",
      "text": "我的",
      "iconPath": "images/tab/profile.png",
      "selectedIconPath": "images/tab/profile-active.png"
    }
  ]
}
```

## 方案三：继续使用纯文字tabBar
当前配置已经移除了图标路径，使用纯文字显示，可以正常运行。
