# 小柿子·幼儿识字辅助工具

## bug修复：
  - 重要提示：相关源码在 wx 目录下，不要改其他目录下的文件
  - [ ] 添加字词页面：
    - 去掉该页面单个添加功能，只保留批量导入功能
    - 批量导入功能：
      - 支持多种分隔符自动识别
      - 支持导入多个字词
      - 支持导入单个字词

## 项目简介

小柿子·幼儿识字辅助工具是一款专为3-6岁幼儿设计的微信小程序，基于艾宾浩斯遗忘曲线科学规划复习计划，帮助家长陪伴孩子高效学习识字。

## 核心功能

### 1. 新手引导
- 功能介绍和使用指导
- 首次启动引导流程
- 幼儿信息设置引导
  - 页面链接: `pages/onboarding/onboarding`

### 2. 今日复习（首页）
- 显示今日复习任务和进度
- 快速添加新字词
- 学习统计概览
- 一键开始复习
  - 页面链接: `pages/home/<USER>

### 3. 字词复习
- 大字显示，护眼设计
- 简单的"已掌握/需复习"操作
- 亲子互动指导
- 复习进度跟踪
  - 页面链接: `pages/word-review/word-review`

### 4. 智能复习计划
- 基于艾宾浩斯遗忘曲线
- 自动生成复习计划
- 复习日历展示
- 薄弱字词专项复习
  - 页面链接: `pages/review/review`

### 5. 字词管理
- 手动添加单个字词
- 批量导入多个字词
- 支持多种分隔符自动识别
- 字词库管理
  - 页面链接: `pages/add-words/add-words`

### 6. 幼儿信息管理
- 多幼儿账号支持
- 个性化学习数据
- 年龄自动计算
- 账号切换功能
  - 页面链接: `pages/child-management/child-management`

### 7. 学习记录
- 详细的学习统计
- 学习日历展示
- 进度分析报告
- 数据导出功能
  - 页面链接: `pages/learning-records/learning-records`

### 8. 设置与数据管理
- 数据导入导出
- 缓存清理
- 数据安全提示
- 版本更新检查
  - 页面链接: `pages/settings/settings`

## 技术特点

### 1. 本地数据存储
- 所有数据保存在设备本地
- 无需联网，保护隐私
- 支持数据导入导出备份

### 2. 护眼设计
- 低饱和度色调
- 大字体显示
- 柔和的视觉效果
- 适合幼儿使用

### 3. 科学复习算法
- 艾宾浩斯遗忘曲线
- 智能复习提醒
- 个性化复习计划
- 薄弱字词识别

### 4. 极简交互
- 操作简单直观
- 减少层级跳转
- 突出核心功能
- 适合亲子使用

## 项目结构

```
wx/
├── app.js                 # 小程序主入口
├── app.json              # 小程序配置
├── app.wxss              # 全局样式
├── sitemap.json          # 站点地图
├── project.config.json   # 项目配置
├── utils/                # 工具类
│   ├── dataManager.js    # 数据管理器
│   └── ebbinghausManager.js # 艾宾浩斯算法管理器
└── pages/                # 页面目录
    ├── onboarding/       # 新手引导
    ├── home/            # 首页
    ├── word-review/     # 字词复习
    ├── review/          # 复习页面
    ├── profile/         # 我的页面
    ├── add-words/       # 添加字词
    ├── child-management/ # 幼儿管理
    ├── settings/        # 设置页面
    └── learning-records/ # 学习记录
```

## 数据结构

### 用户信息
```javascript
{
  user_id: "用户ID",
  children_info: [
    {
      child_id: "幼儿ID",
      child_name: "幼儿姓名",
      birthday: "2020-01",
      class_type: "中班",
      age: 4,
      create_time: "创建时间"
    }
  ],
  create_time: "创建时间"
}
```

### 字词库
```javascript
{
  word_id: "字词ID",
  child_id: "幼儿ID",
  word_text: "字词内容",
  mastered_status: false,
  review_times: 0,
  last_review_date: "最后复习日期",
  create_time: "创建时间"
}
```

### 复习计划
```javascript
{
  plan_id: "计划ID",
  child_id: "幼儿ID",
  word_id: "字词ID",
  review_date: "复习日期",
  review_stage: 1,
  completed: false,
  create_time: "创建时间"
}
```

### 学习记录
```javascript
{
  record_id: "记录ID",
  child_id: "幼儿ID",
  learn_date: "学习日期",
  word_count: 10,
  mastered_count: 8,
  weak_words: ["薄弱字词"],
  create_time: "创建时间"
}
```

## 使用说明

### 1. 开发环境
- 微信开发者工具
- 小程序基础库 2.19.4+

### 2. 部署步骤
1. 下载微信开发者工具
2. 导入项目到开发者工具
3. 修改 `project.config.json` 中的 `appid`
4. 预览或上传代码

### 3. 自定义配置
- 修改 `app.wxss` 中的颜色变量
- 调整 `ebbinghausManager.js` 中的复习间隔
- 自定义 `app.json` 中的页面配置

## 注意事项

### 1. 数据安全
- 所有数据仅保存在本地
- 删除小程序会丢失所有数据
- 建议定期导出数据备份

### 2. 兼容性
- 支持微信 7.0.0 以上版本
- 兼容 iOS 和 Android 系统
- 建议在真机上测试

### 3. 性能优化
- 使用懒加载减少内存占用
- 限制数据量避免卡顿
- 定期清理过期数据

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 完整的识字复习功能
- 艾宾浩斯遗忘曲线算法
- 多幼儿账号支持
- 数据导入导出功能

## 技术支持

如有问题或建议，请通过以下方式联系：
- 小程序内意见反馈
- 关注公众号获取支持

## 开源协议

本项目采用 MIT 开源协议，详见 LICENSE 文件。
