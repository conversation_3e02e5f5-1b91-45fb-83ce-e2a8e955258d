/* 复习页面样式 */

.review-container {
  background: var(--warm-beige);
  min-height: 100vh;
  padding: 32rpx;
}

/* 统计卡片 */
.stats-card {
  background: linear-gradient(135deg, var(--primary-green), var(--light-green));
  color: white;
  margin-bottom: 32rpx;
}

.stats-card .card-title {
  color: white;
  margin-bottom: 32rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32rpx;
  text-align: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
}

.stat-number.primary {
  color: white;
}

.stat-number.orange {
  color: #FFE4B5;
}

.stat-number.blue {
  color: #E6F3FF;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.task-count {
  background: var(--light-green);
  color: var(--text-dark);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.weak-count {
  background: #FFE4B5;
  color: var(--soft-orange);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.view-more {
  font-size: 24rpx;
  color: var(--primary-green);
}

/* 今日任务 */
.today-tasks {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.task-progress {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.progress-text {
  font-size: 24rpx;
  color: var(--text-light);
  text-align: center;
}

.task-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.task-item {
  background: var(--soft-green);
  padding: 16rpx 20rpx;
  border-radius: 20rpx;
  border: 1px solid var(--light-green);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
  min-width: 120rpx;
}

.task-word {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-dark);
}

.task-stage {
  font-size: 20rpx;
  color: var(--text-light);
}

.start-review-btn {
  width: 100%;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
}

/* 空任务状态 */
.empty-tasks {
  text-align: center;
  padding: 60rpx 32rpx;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}

.empty-text {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: var(--text-light);
}

/* 本周日历 */
.weekly-calendar {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
}

.calendar-day {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 8rpx;
  border-radius: 16rpx;
  background: var(--warm-beige);
  border: 1px solid var(--border-light);
  position: relative;
  transition: all 0.3s ease;
}

.calendar-day.today {
  background: var(--primary-green);
  color: white;
}

.calendar-day.has-tasks {
  background: var(--soft-green);
  border-color: var(--light-green);
}

.calendar-day.today.has-tasks {
  background: var(--primary-green);
}

.day-name {
  font-size: 20rpx;
  margin-bottom: 8rpx;
  opacity: 0.8;
}

.day-number {
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.day-tasks {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.task-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
}

.task-dot.completed {
  background: var(--primary-green);
}

.task-dot.pending {
  background: var(--soft-orange);
}

.calendar-day.today .task-dot.completed {
  background: white;
}

.task-count {
  font-size: 18rpx;
}

/* 薄弱字词 */
.weak-words-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.weak-word-item {
  background: #FFF3E0;
  padding: 20rpx;
  border-radius: 16rpx;
  border: 1px solid #FFE0B2;
  text-align: center;
}

.weak-word-text {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 8rpx;
}

.weak-word-times {
  font-size: 20rpx;
  color: var(--soft-orange);
}

.start-weak-review-btn {
  width: 100%;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
}

/* 学习记录 */
.record-summary {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32rpx;
  margin-bottom: 24rpx;
  text-align: center;
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.summary-label {
  font-size: 24rpx;
  color: var(--text-light);
}

.summary-value {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--primary-green);
}

.view-records-btn {
  width: 100%;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 24rpx;
  }
  
  .weak-words-list {
    grid-template-columns: 1fr;
  }
  
  .record-summary {
    grid-template-columns: 1fr;
    gap: 24rpx;
  }
}
