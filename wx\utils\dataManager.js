/**
 * 数据管理器
 * 负责本地数据的存储、读取和管理
 */

class DataManager {
  constructor() {
    this.storageKeys = {
      USER_INFO: 'user_info',
      WORD_LIBRARY: 'word_library',
      LEARNING_RECORDS: 'learning_records',
      REVIEW_PLANS: 'review_plans'
    };
  }

  /**
   * 生成UUID
   */
  generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * 获取当前日期字符串
   */
  getCurrentDate() {
    const now = new Date();
    return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
  }

  /**
   * 获取当前时间字符串
   */
  getCurrentDateTime() {
    const now = new Date();
    return `${this.getCurrentDate()} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
  }

  /**
   * 获取用户信息
   */
  getUserInfo() {
    try {
      return wx.getStorageSync(this.storageKeys.USER_INFO);
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return null;
    }
  }

  /**
   * 保存用户信息
   */
  saveUserInfo(userInfo) {
    try {
      wx.setStorageSync(this.storageKeys.USER_INFO, userInfo);
      return true;
    } catch (error) {
      console.error('保存用户信息失败:', error);
      return false;
    }
  }

  /**
   * 创建新用户
   */
  createUser(openId) {
    const userInfo = {
      user_id: openId,
      children_info: [],
      create_time: this.getCurrentDateTime()
    };
    return this.saveUserInfo(userInfo);
  }

  /**
   * 添加幼儿信息
   */
  addChild(childInfo) {
    const userInfo = this.getUserInfo();
    if (!userInfo) return false;

    const child = {
      child_id: this.generateUUID(),
      child_name: childInfo.name,
      birthday: childInfo.birthday,
      class_type: childInfo.classType,
      age: this.calculateAge(childInfo.birthday),
      last_learn_date: '',
      create_time: this.getCurrentDateTime()
    };

    userInfo.children_info.push(child);
    return this.saveUserInfo(userInfo);
  }

  /**
   * 计算年龄
   */
  calculateAge(birthday) {
    const [year, month] = birthday.split('-').map(Number);
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;
    
    let age = currentYear - year;
    if (currentMonth < month) {
      age--;
    }
    return age;
  }

  /**
   * 获取字词库
   */
  getWordLibrary(childId = null) {
    try {
      const wordLibrary = wx.getStorageSync(this.storageKeys.WORD_LIBRARY) || [];
      if (childId) {
        return wordLibrary.filter(word => word.child_id === childId);
      }
      return wordLibrary;
    } catch (error) {
      console.error('获取字词库失败:', error);
      return [];
    }
  }

  /**
   * 保存字词库
   */
  saveWordLibrary(wordLibrary) {
    try {
      wx.setStorageSync(this.storageKeys.WORD_LIBRARY, wordLibrary);
      return true;
    } catch (error) {
      console.error('保存字词库失败:', error);
      return false;
    }
  }

  /**
   * 添加字词
   */
  addWords(childId, words) {
    const wordLibrary = this.getWordLibrary();
    const newWords = words.map(wordText => ({
      word_id: this.generateUUID(),
      child_id: childId,
      word_text: wordText.trim(),
      mastered_status: false,
      review_times: 0,
      last_review_date: '',
      create_time: this.getCurrentDateTime()
    }));

    wordLibrary.push(...newWords);
    return this.saveWordLibrary(wordLibrary);
  }

  /**
   * 更新字词状态
   */
  updateWordStatus(wordId, masteredStatus) {
    const wordLibrary = this.getWordLibrary();
    const wordIndex = wordLibrary.findIndex(word => word.word_id === wordId);
    
    if (wordIndex !== -1) {
      wordLibrary[wordIndex].mastered_status = masteredStatus;
      wordLibrary[wordIndex].review_times += 1;
      wordLibrary[wordIndex].last_review_date = this.getCurrentDate();
      return this.saveWordLibrary(wordLibrary);
    }
    return false;
  }

  /**
   * 获取学习记录
   */
  getLearningRecords(childId = null) {
    try {
      const records = wx.getStorageSync(this.storageKeys.LEARNING_RECORDS) || [];
      if (childId) {
        return records.filter(record => record.child_id === childId);
      }
      return records;
    } catch (error) {
      console.error('获取学习记录失败:', error);
      return [];
    }
  }

  /**
   * 保存学习记录
   */
  saveLearningRecord(childId, wordCount, masteredCount, weakWords) {
    const records = this.getLearningRecords();
    const userInfo = this.getUserInfo();
    
    const record = {
      record_id: this.generateUUID(),
      child_id: childId,
      user_id: userInfo.user_id,
      learn_date: this.getCurrentDate(),
      word_count: wordCount,
      mastered_count: masteredCount,
      weak_words: weakWords,
      create_time: this.getCurrentDateTime()
    };

    records.push(record);
    
    try {
      wx.setStorageSync(this.storageKeys.LEARNING_RECORDS, records);
      return true;
    } catch (error) {
      console.error('保存学习记录失败:', error);
      return false;
    }
  }

  /**
   * 获取复习计划
   */
  getReviewPlans(childId = null) {
    try {
      const plans = wx.getStorageSync(this.storageKeys.REVIEW_PLANS) || [];
      if (childId) {
        return plans.filter(plan => plan.child_id === childId);
      }
      return plans;
    } catch (error) {
      console.error('获取复习计划失败:', error);
      return [];
    }
  }

  /**
   * 保存复习计划
   */
  saveReviewPlans(plans) {
    try {
      wx.setStorageSync(this.storageKeys.REVIEW_PLANS, plans);
      return true;
    } catch (error) {
      console.error('保存复习计划失败:', error);
      return false;
    }
  }

  /**
   * 清除所有数据
   */
  clearAllData() {
    try {
      Object.values(this.storageKeys).forEach(key => {
        wx.removeStorageSync(key);
      });
      return true;
    } catch (error) {
      console.error('清除数据失败:', error);
      return false;
    }
  }
}

module.exports = DataManager;
