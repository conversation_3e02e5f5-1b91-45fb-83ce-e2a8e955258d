/* 新手引导页面样式 */

.onboarding-container {
  height: 100vh;
  background: linear-gradient(135deg, var(--primary-green), var(--light-green));
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.status-bar-placeholder {
  height: 44px;
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0 32rpx;
}

.guide-swiper {
  flex: 1;
  height: 100%;
}

.swiper-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.step-content {
  text-align: center;
  color: white;
  max-width: 600rpx;
}

.step-icon {
  font-size: 160rpx;
  margin-bottom: 40rpx;
  line-height: 1;
}

.step-title {
  font-size: 60rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
  line-height: 1.2;
}

.step-subtitle {
  font-size: 36rpx;
  margin-bottom: 32rpx;
  opacity: 0.9;
  line-height: 1.3;
}

.step-description {
  font-size: 28rpx;
  line-height: 1.6;
  opacity: 0.8;
  padding: 0 20rpx;
}

.indicators {
  display: flex;
  justify-content: center;
  gap: 16rpx;
  margin: 40rpx 0;
}

.indicator {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.indicator.active {
  background: white;
  transform: scale(1.2);
}

.feature-highlight {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 24rpx;
  padding: 32rpx;
  margin: 20rpx 0;
  text-align: center;
  color: white;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.highlight-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.highlight-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.highlight-desc {
  font-size: 24rpx;
  line-height: 1.5;
  opacity: 0.9;
}

.bottom-actions {
  padding: 32rpx;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
}

.nav-buttons {
  display: flex;
  gap: 24rpx;
  margin-bottom: 24rpx;
}

.btn-small {
  flex: 0 0 auto;
  padding: 20rpx 32rpx;
  font-size: 28rpx;
}

.btn-main {
  flex: 1;
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.btn-outline {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.5);
}

.btn-outline:active {
  background: rgba(255, 255, 255, 0.1);
}

.btn-primary {
  background: white;
  color: var(--primary-green);
}

.btn-primary:active {
  background: rgba(255, 255, 255, 0.9);
  transform: scale(0.98);
}

.skip-button {
  text-align: center;
  margin-bottom: 16rpx;
}

.skip-button text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 28rpx;
  text-decoration: underline;
}

.usage-tip {
  text-align: center;
}

.usage-tip text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}

/* 动画效果 */
.step-content {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(60rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 按钮点击效果 */
button {
  border: none;
  border-radius: 24rpx;
  transition: all 0.2s ease;
}

button:active {
  transform: scale(0.95);
}
