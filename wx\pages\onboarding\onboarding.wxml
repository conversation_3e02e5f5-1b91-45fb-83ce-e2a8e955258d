<!--新手引导页面-->
<view class="onboarding-container">
  <!-- 顶部状态栏占位 -->
  <view class="status-bar-placeholder"></view>
  
  <!-- 主要内容区域 -->
  <view class="content-area">
    <!-- 滑动容器 -->
    <swiper 
      class="guide-swiper" 
      current="{{currentStep}}" 
      bindchange="onSwiperChange"
      indicator-dots="{{false}}"
      autoplay="false"
      duration="300"
    >
      <swiper-item wx:for="{{steps}}" wx:key="index" class="swiper-item">
        <view class="step-content">
          <!-- 图标 -->
          <view class="step-icon">{{item.icon}}</view>
          
          <!-- 标题 -->
          <view class="step-title">{{item.title}}</view>
          
          <!-- 副标题 -->
          <view class="step-subtitle">{{item.subtitle}}</view>
          
          <!-- 描述 -->
          <view class="step-description">{{item.description}}</view>
        </view>
      </swiper-item>
    </swiper>
    
    <!-- 指示器 -->
    <view class="indicators">
      <view 
        wx:for="{{steps}}" 
        wx:key="index"
        class="indicator {{currentStep === index ? 'active' : ''}}"
        data-index="{{index}}"
        bindtap="onIndicatorTap"
      ></view>
    </view>
    
    <!-- 特色说明 -->
    <view class="feature-highlight">
      <view class="highlight-icon">🛡️</view>
      <view class="highlight-title">本地存储，保护隐私</view>
      <view class="highlight-desc">
        所有数据仅保存在您的设备本地，无需联网，保障数据安全。
        告别枯燥机械记忆，用最少的时间收获最佳学习效果。
      </view>
    </view>
  </view>
  
  <!-- 底部操作区域 -->
  <view class="bottom-actions">
    <!-- 导航按钮 -->
    <view class="nav-buttons">
      <button 
        wx:if="{{currentStep > 0}}" 
        class="btn-outline btn-small"
        bindtap="prevStep"
      >
        上一步
      </button>
      
      <button 
        class="btn-primary btn-main"
        bindtap="nextStep"
      >
        {{currentStep === steps.length - 1 ? '开始使用 🚀' : '下一步'}}
      </button>
    </view>
    

    
    <!-- 提示信息 -->
    <view class="usage-tip">
      <text>📱 无需下载，微信内直接使用</text>
    </view>
  </view>
</view>
