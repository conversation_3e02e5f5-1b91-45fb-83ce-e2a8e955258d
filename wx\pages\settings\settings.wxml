<!--设置页面-->
<view class="settings-container">
  <!-- 当前幼儿信息 -->
  <view class="current-child-card" wx:if="{{currentChild}}">
    <view class="child-info">
      <view class="child-avatar">👶</view>
      <view class="child-details">
        <view class="child-name">{{currentChild.child_name}}</view>
        <view class="child-meta">{{currentChild.class_type}} · {{currentChild.age}}岁</view>
      </view>
    </view>
  </view>

  <!-- 数据统计 -->
  <view class="card">
    <view class="card-title">📊 数据统计</view>
    <view class="stats-grid">
      <view class="stat-item">
        <view class="stat-number">{{dataStats.totalWords}}</view>
        <view class="stat-label">字词总数</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{dataStats.totalRecords}}</view>
        <view class="stat-label">学习记录</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{dataStats.totalPlans}}</view>
        <view class="stat-label">复习计划</view>
      </view>
    </view>
  </view>

  <!-- 存储空间 -->
  <view class="card">
    <view class="card-title">💾 存储空间</view>
    <view class="storage-info">
      <view class="storage-text">
        已使用 {{storageInfo.used}}MB / {{storageInfo.total}}MB
      </view>
      <view class="storage-bar">
        <view class="storage-fill" style="width: {{storageInfo.percentage}}%"></view>
      </view>
      <view class="storage-percentage">{{storageInfo.percentage}}%</view>
    </view>
  </view>

  <!-- 数据管理 -->
  <view class="card">
    <view class="card-title">🗂️ 数据管理</view>
    <view class="settings-list">
      <view class="setting-item" bindtap="exportData">
        <view class="setting-icon">📤</view>
        <view class="setting-content">
          <view class="setting-title">导出数据</view>
          <view class="setting-desc">备份学习数据到本地</view>
        </view>
        <view class="setting-arrow">→</view>
      </view>
      
      <view class="setting-item" bindtap="importData">
        <view class="setting-icon">📥</view>
        <view class="setting-content">
          <view class="setting-title">导入数据</view>
          <view class="setting-desc">从备份文件恢复数据</view>
        </view>
        <view class="setting-arrow">→</view>
      </view>
      
      <view class="setting-item" bindtap="clearCache">
        <view class="setting-icon">🧹</view>
        <view class="setting-content">
          <view class="setting-title">清除缓存</view>
          <view class="setting-desc">清理临时文件和缓存</view>
        </view>
        <view class="setting-arrow">→</view>
      </view>
    </view>
  </view>

  <!-- 应用设置 -->
  <view class="card">
    <view class="card-title">⚙️ 应用设置</view>
    <view class="settings-list">
      <view class="setting-item" bindtap="followOfficialAccount">
        <view class="setting-icon">📱</view>
        <view class="setting-content">
          <view class="setting-title">关注公众号</view>
          <view class="setting-desc">获取更新和技术支持</view>
        </view>
        <view class="setting-arrow">→</view>
      </view>
      
      <view class="setting-item" bindtap="checkUpdate">
        <view class="setting-icon">🔄</view>
        <view class="setting-content">
          <view class="setting-title">检查更新</view>
          <view class="setting-desc">查看是否有新版本</view>
        </view>
        <view class="setting-arrow">→</view>
      </view>
    </view>
  </view>

  <!-- 危险操作 -->
  <view class="card danger-card">
    <view class="card-title danger-title">⚠️ 危险操作</view>
    <view class="danger-item" bindtap="clearAllData">
      <view class="danger-icon">🗑️</view>
      <view class="danger-content">
        <view class="danger-title">清除所有数据</view>
        <view class="danger-desc">删除所有学习数据，不可恢复</view>
      </view>
      <view class="danger-arrow">→</view>
    </view>
  </view>

  <!-- 数据安全提示 -->
  <view class="data-notice">
    <view class="notice-icon">🛡️</view>
    <view class="notice-content">
      <view class="notice-title">数据安全提示</view>
      <view class="notice-text">
        本工具所有学习数据仅保存在您的设备本地，一旦删除本程序或设备故障导致数据丢失，无法通过任何方式找回。建议定期导出数据进行备份。
      </view>
    </view>
  </view>

  <!-- 版本信息 -->
  <view class="version-info">
    <text>小柿子识字 v1.0.0</text>
    <text>专为3-6岁幼儿设计</text>
  </view>
</view>
