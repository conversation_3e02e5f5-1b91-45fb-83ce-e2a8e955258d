/* 幼儿信息管理页面样式 */

.child-management-container {
  background: var(--warm-beige);
  min-height: 100vh;
  padding: 32rpx;
}

/* 页面头部 */
.page-header, .form-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.page-title, .form-title {
  font-size: 40rpx;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 12rpx;
}

.page-desc, .form-desc {
  font-size: 28rpx;
  color: var(--text-light);
}

/* 幼儿列表 */
.children-list {
  margin-bottom: 32rpx;
}

.child-item {
  background: white;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: var(--shadow-soft);
  border: 1px solid var(--border-light);
}

.child-info {
  display: flex;
  align-items: center;
  padding: 32rpx;
  gap: 24rpx;
}

.child-avatar {
  width: 100rpx;
  height: 100rpx;
  background: var(--soft-green);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 50rpx;
}

.child-details {
  flex: 1;
}

.child-name {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 8rpx;
}

.child-meta {
  font-size: 28rpx;
  color: var(--text-light);
  margin-bottom: 8rpx;
}

.child-birthday {
  font-size: 24rpx;
  color: var(--text-light);
}

.child-status {
  margin-left: 16rpx;
}

.current-tag {
  background: var(--primary-green);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.child-actions {
  display: flex;
  border-top: 1px solid var(--border-light);
}

.action-btn {
  flex: 1;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  background: white;
  color: var(--text-dark);
}

.action-btn:first-child {
  border-right: 1px solid var(--border-light);
}

.edit-btn:active {
  background: var(--soft-green);
}

.delete-btn {
  color: #ff4444;
}

.delete-btn:active {
  background: #ffebee;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 32rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: var(--text-light);
  line-height: 1.5;
}

/* 添加按钮 */
.add-button-container {
  margin-top: 32rpx;
}

.add-child-btn {
  width: 100%;
  padding: 32rpx;
  font-size: 32rpx;
  font-weight: 600;
}

/* 表单样式 */
.form-content {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: var(--shadow-soft);
  border: 1px solid var(--border-light);
}

.form-group {
  margin-bottom: 40rpx;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.form-input {
  width: 100%;
  height: 80rpx; /* 增加高度 */
  padding: 24rpx;
  border: 2px solid var(--border-light);
  border-radius: 16rpx;
  font-size: 32rpx;
  background: var(--warm-beige);
  box-sizing: border-box;
}

.form-input::placeholder {
  color: var(--text-light);
}

.picker-display {
  color: var(--text-light);
}

.picker-display.selected {
  color: var(--text-dark);
}

.form-input:focus {
  border-color: var(--primary-green);
}

/* 生日选择器 */
.birthday-selectors {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.birthday-picker, .class-picker {
  border: 2px solid var(--border-light);
  border-radius: 16rpx;
  background: var(--warm-beige);
}

.picker-display {
  padding: 24rpx;
  font-size: 32rpx;
  color: var(--text-dark);
  text-align: center;
}

.picker-display.full-width {
  text-align: left;
}

/* 年龄显示 */
.age-display {
  background: var(--soft-green);
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid var(--light-green);
}

.age-label {
  font-size: 28rpx;
  color: var(--text-dark);
  font-weight: 500;
}

.age-value {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--primary-green);
}

/* 表单操作 */
.form-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.cancel-btn, .save-btn {
  padding: 32rpx;
  font-size: 32rpx;
  font-weight: 600;
}

/* 使用说明 */
.usage-tips {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-top: 32rpx;
  box-shadow: var(--shadow-soft);
  border: 1px solid var(--border-light);
}

.tips-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.tip-item {
  font-size: 26rpx;
  color: var(--text-light);
  line-height: 1.5;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .child-info {
    flex-direction: column;
    text-align: center;
    gap: 16rpx;
  }
  
  .birthday-selectors {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    grid-template-columns: 1fr;
  }
}
