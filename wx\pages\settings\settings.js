/**
 * 设置页面
 */

const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    currentChild: null,
    storageInfo: {
      used: 0,
      total: 10,
      percentage: 0
    },
    dataStats: {
      totalWords: 0,
      totalRecords: 0,
      totalPlans: 0
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.loadData();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.loadData();
  },

  /**
   * 加载数据
   */
  loadData: function() {
    const currentChild = app.getCurrentChild();
    this.setData({
      currentChild
    });

    this.loadStorageInfo();
    this.loadDataStats();
  },

  /**
   * 加载存储信息
   */
  loadStorageInfo: function() {
    try {
      const storageInfo = wx.getStorageInfoSync();
      const usedMB = (storageInfo.currentSize / 1024).toFixed(2);
      const totalMB = 10; // 微信小程序限制10MB
      const percentage = Math.min((usedMB / totalMB) * 100, 100);

      this.setData({
        storageInfo: {
          used: usedMB,
          total: totalMB,
          percentage: percentage.toFixed(1)
        }
      });
    } catch (error) {
      console.error('获取存储信息失败:', error);
    }
  },

  /**
   * 加载数据统计
   */
  loadDataStats: function() {
    const wordLibrary = app.dataManager.getWordLibrary();
    const learningRecords = app.dataManager.getLearningRecords();
    const reviewPlans = app.dataManager.getReviewPlans();

    this.setData({
      dataStats: {
        totalWords: wordLibrary.length,
        totalRecords: learningRecords.length,
        totalPlans: reviewPlans.length
      }
    });
  },

  /**
   * 导出数据
   */
  exportData: function() {
    wx.showModal({
      title: '导出数据',
      content: '将学习数据导出为文本格式，您可以保存到相册或分享给其他人',
      confirmText: '导出',
      success: (res) => {
        if (res.confirm) {
          this.performExportData();
        }
      }
    });
  },

  /**
   * 执行数据导出
   */
  performExportData: function() {
    wx.showLoading({
      title: '导出中...'
    });

    try {
      const userInfo = app.dataManager.getUserInfo();
      const wordLibrary = app.dataManager.getWordLibrary();
      const learningRecords = app.dataManager.getLearningRecords();
      const reviewPlans = app.dataManager.getReviewPlans();

      const exportData = {
        version: '1.0.0',
        exportTime: app.dataManager.getCurrentDateTime(),
        userInfo,
        wordLibrary,
        learningRecords,
        reviewPlans
      };

      const dataText = JSON.stringify(exportData, null, 2);
      
      // 创建临时文件
      const fs = wx.getFileSystemManager();
      const fileName = `小柿子识字数据_${Date.now()}.json`;
      const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`;
      
      fs.writeFileSync(filePath, dataText, 'utf8');
      
      wx.hideLoading();
      
      // 分享文件
      wx.shareFileMessage({
        filePath: filePath,
        fileName: fileName,
        success: () => {
          wx.showToast({
            title: '导出成功',
            icon: 'success'
          });
        },
        fail: (error) => {
          console.error('分享失败:', error);
          wx.showModal({
            title: '导出完成',
            content: '数据已导出，但分享失败。您可以在文件管理中找到导出的文件。',
            showCancel: false
          });
        }
      });
    } catch (error) {
      wx.hideLoading();
      console.error('导出失败:', error);
      wx.showToast({
        title: '导出失败',
        icon: 'none'
      });
    }
  },

  /**
   * 导入数据
   */
  importData: function() {
    wx.showModal({
      title: '导入数据',
      content: '导入数据将覆盖当前所有数据，请确保已备份重要信息。是否继续？',
      confirmText: '继续',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          this.chooseImportFile();
        }
      }
    });
  },

  /**
   * 选择导入文件
   */
  chooseImportFile: function() {
    wx.chooseMessageFile({
      count: 1,
      type: 'file',
      extension: ['json'],
      success: (res) => {
        const file = res.tempFiles[0];
        this.performImportData(file.path);
      },
      fail: () => {
        wx.showToast({
          title: '请选择JSON文件',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 执行数据导入
   */
  performImportData: function(filePath) {
    wx.showLoading({
      title: '导入中...'
    });

    try {
      const fs = wx.getFileSystemManager();
      const dataText = fs.readFileSync(filePath, 'utf8');
      const importData = JSON.parse(dataText);

      // 验证数据格式
      if (!this.validateImportData(importData)) {
        wx.hideLoading();
        wx.showToast({
          title: '数据格式错误',
          icon: 'none'
        });
        return;
      }

      // 导入数据
      if (importData.userInfo) {
        app.dataManager.saveUserInfo(importData.userInfo);
      }
      if (importData.wordLibrary) {
        app.dataManager.saveWordLibrary(importData.wordLibrary);
      }
      if (importData.learningRecords) {
        wx.setStorageSync('learning_records', importData.learningRecords);
      }
      if (importData.reviewPlans) {
        app.dataManager.saveReviewPlans(importData.reviewPlans);
      }

      wx.hideLoading();
      
      wx.showModal({
        title: '导入成功',
        content: '数据导入完成，请重启小程序以确保数据正确加载。',
        confirmText: '重启',
        showCancel: false,
        success: () => {
          wx.reLaunch({
            url: '/pages/home/<USER>'
          });
        }
      });
    } catch (error) {
      wx.hideLoading();
      console.error('导入失败:', error);
      wx.showToast({
        title: '导入失败，请检查文件格式',
        icon: 'none'
      });
    }
  },

  /**
   * 验证导入数据格式
   */
  validateImportData: function(data) {
    if (!data || typeof data !== 'object') return false;
    if (!data.version) return false;
    
    // 基本格式验证
    const requiredFields = ['userInfo', 'wordLibrary', 'learningRecords', 'reviewPlans'];
    return requiredFields.some(field => data[field]);
  },

  /**
   * 清除缓存
   */
  clearCache: function() {
    wx.showModal({
      title: '清除缓存',
      content: '清除缓存不会删除学习数据，只会清理临时文件和缓存信息。',
      confirmText: '清除',
      success: (res) => {
        if (res.confirm) {
          this.performClearCache();
        }
      }
    });
  },

  /**
   * 执行清除缓存
   */
  performClearCache: function() {
    wx.showLoading({
      title: '清除中...'
    });

    try {
      // 清除临时文件
      const fs = wx.getFileSystemManager();
      try {
        const files = fs.readdirSync(wx.env.USER_DATA_PATH);
        files.forEach(file => {
          if (file.endsWith('.json') || file.endsWith('.tmp')) {
            fs.unlinkSync(`${wx.env.USER_DATA_PATH}/${file}`);
          }
        });
      } catch (error) {
        console.log('清除临时文件失败:', error);
      }

      wx.hideLoading();
      
      wx.showToast({
        title: '清除完成',
        icon: 'success'
      });

      this.loadStorageInfo();
    } catch (error) {
      wx.hideLoading();
      console.error('清除缓存失败:', error);
      wx.showToast({
        title: '清除失败',
        icon: 'none'
      });
    }
  },

  /**
   * 清除所有数据
   */
  clearAllData: function() {
    wx.showModal({
      title: '危险操作',
      content: '此操作将删除所有学习数据，包括幼儿信息、字词库、学习记录等，且无法恢复。确定要继续吗？',
      confirmText: '确定删除',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          this.confirmClearAllData();
        }
      }
    });
  },

  /**
   * 二次确认清除所有数据
   */
  confirmClearAllData: function() {
    wx.showModal({
      title: '最后确认',
      content: '您真的要删除所有数据吗？此操作不可撤销！',
      confirmText: '删除',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          this.performClearAllData();
        }
      }
    });
  },

  /**
   * 执行清除所有数据
   */
  performClearAllData: function() {
    wx.showLoading({
      title: '删除中...'
    });

    try {
      // 清除所有存储数据
      app.dataManager.clearAllData();
      
      // 重置全局状态
      app.globalData.userInfo = null;
      app.globalData.currentChild = null;

      wx.hideLoading();
      
      wx.showModal({
        title: '删除完成',
        content: '所有数据已删除，将返回引导页面。',
        confirmText: '确定',
        showCancel: false,
        success: () => {
          wx.reLaunch({
            url: '/pages/onboarding/onboarding'
          });
        }
      });
    } catch (error) {
      wx.hideLoading();
      console.error('删除数据失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    }
  },

  /**
   * 关注公众号
   */
  followOfficialAccount: function() {
    wx.showModal({
      title: '关注公众号',
      content: '如有问题或建议，请关注我们的公众号进行反馈和交流。',
      confirmText: '好的',
      showCancel: false
    });
  },

  /**
   * 检查更新
   */
  checkUpdate: function() {
    const updateManager = wx.getUpdateManager();
    
    updateManager.onCheckForUpdate((res) => {
      if (res.hasUpdate) {
        wx.showModal({
          title: '发现新版本',
          content: '发现新版本，是否立即更新？',
          success: (res) => {
            if (res.confirm) {
              updateManager.onUpdateReady(() => {
                updateManager.applyUpdate();
              });
            }
          }
        });
      } else {
        wx.showToast({
          title: '已是最新版本',
          icon: 'success'
        });
      }
    });

    updateManager.onUpdateFailed(() => {
      wx.showToast({
        title: '更新失败',
        icon: 'none'
      });
    });
  }
});
