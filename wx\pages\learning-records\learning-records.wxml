<!--学习记录页面-->
<view class="records-container">
  <!-- 幼儿信息 -->
  <view class="child-header" wx:if="{{currentChild}}">
    <view class="child-avatar">👶</view>
    <view class="child-info">
      <view class="child-name">{{currentChild.child_name}}</view>
      <view class="child-meta">{{currentChild.class_type}} · {{currentChild.age}}岁</view>
    </view>
  </view>

  <!-- 学习统计摘要 -->
  <view class="summary-card">
    <view class="summary-title">📊 学习统计</view>
    <view class="summary-grid">
      <view class="summary-item">
        <view class="summary-number">{{summary.totalDays}}</view>
        <view class="summary-label">学习天数</view>
      </view>
      <view class="summary-item">
        <view class="summary-number">{{summary.totalWords}}</view>
        <view class="summary-label">总字词数</view>
      </view>
      <view class="summary-item">
        <view class="summary-number">{{summary.totalMastered}}</view>
        <view class="summary-label">已掌握</view>
      </view>
      <view class="summary-item">
        <view class="summary-number">{{summary.averageAccuracy}}%</view>
        <view class="summary-label">平均正确率</view>
      </view>
    </view>
  </view>

  <!-- 本月记录 -->
  <view class="card" wx:if="{{monthRecords.length > 0}}">
    <view class="card-header">
      <view class="card-title">📅 {{currentMonth}}</view>
      <text class="month-count">{{monthRecords.length}}天</text>
    </view>
    
    <view class="month-calendar">
      <view 
        wx:for="{{monthRecords}}" 
        wx:key="record_id"
        class="calendar-day"
        data-record-id="{{item.record_id}}"
        bindtap="viewRecordDetail"
      >
        <view class="day-date">{{item.dateDisplay}}</view>
        <view class="day-weekday">{{item.weekday}}</view>
        <view class="day-stats">
          <view class="day-accuracy {{item.accuracy >= 80 ? 'good' : (item.accuracy >= 60 ? 'normal' : 'poor')}}">
            {{item.accuracy}}%
          </view>
          <view class="day-words">{{item.word_count}}词</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 详细记录列表 -->
  <view class="card">
    <view class="card-header">
      <view class="card-title">📝 学习记录</view>
      <text class="export-btn" bindtap="exportRecords" wx:if="{{records.length > 0}}">
        导出 📤
      </text>
    </view>
    
    <view wx:if="{{records.length > 0}}" class="records-list">
      <view 
        wx:for="{{records}}" 
        wx:key="record_id"
        class="record-item"
      >
        <view class="record-main" data-record-id="{{item.record_id}}" bindtap="viewRecordDetail">
          <view class="record-date">
            <view class="date-text">{{item.dateDisplay}}</view>
            <view class="weekday-text">{{item.weekday}}</view>
          </view>
          
          <view class="record-stats">
            <view class="stats-row">
              <view class="stat-item">
                <text class="stat-label">学习</text>
                <text class="stat-value">{{item.word_count}}词</text>
              </view>
              <view class="stat-item">
                <text class="stat-label">掌握</text>
                <text class="stat-value">{{item.mastered_count}}词</text>
              </view>
              <view class="stat-item">
                <text class="stat-label">正确率</text>
                <text class="stat-value accuracy {{item.accuracy >= 80 ? 'good' : (item.accuracy >= 60 ? 'normal' : 'poor')}}">
                  {{item.accuracy}}%
                </text>
              </view>
            </view>
            
            <view class="weak-words" wx:if="{{item.weak_words && item.weak_words.length > 0}}">
              <text class="weak-label">薄弱：</text>
              <text class="weak-list">{{item.weak_words.join('、')}}</text>
            </view>
          </view>
        </view>
        
        <view class="record-actions">
          <button 
            class="delete-btn" 
            data-record-id="{{item.record_id}}"
            bindtap="deleteRecord"
          >
            删除
          </button>
        </view>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view wx:else class="empty-records">
      <view class="empty-icon">📊</view>
      <view class="empty-title">暂无学习记录</view>
      <view class="empty-desc">完成字词复习后会自动生成学习记录</view>
    </view>
  </view>

  <!-- 使用说明 -->
  <view class="usage-tips">
    <view class="tips-title">💡 说明</view>
    <view class="tips-content">
      <view class="tip-item">• 每次完成复习会自动生成学习记录</view>
      <view class="tip-item">• 点击记录可查看详细信息</view>
      <view class="tip-item">• 正确率80%以上为优秀，60%以上为良好</view>
      <view class="tip-item">• 可导出记录进行备份和分析</view>
    </view>
  </view>
</view>
