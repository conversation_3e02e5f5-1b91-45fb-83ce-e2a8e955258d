<!--我的页面 - 个人中心-->
<view class="profile-container">
  <!-- 用户信息卡片 -->
  <view class="user-card">
    <view class="user-info">
      <view class="user-avatar">
        <text class="avatar-icon">👶</text>
      </view>
      <view class="user-details">
        <view class="user-name" wx:if="{{currentChild}}">
          {{currentChild.child_name}}
        </view>
        <view class="user-name" wx:else>
          未设置幼儿信息
        </view>
        <view class="user-meta" wx:if="{{currentChild}}">
          {{currentChild.class_type}} · {{currentChild.age}}岁
        </view>
        <view class="user-meta" wx:else>
          点击设置幼儿信息
        </view>
      </view>
      <view class="switch-btn" bindtap="switchChild">
        <text class="switch-icon">🔄</text>
      </view>
    </view>
    
    <!-- 学习统计 -->
    <view class="stats-section" wx:if="{{currentChild}}">
      <view class="stats-grid">
        <view class="stat-item">
          <view class="stat-number">{{totalWords}}</view>
          <view class="stat-label">总字词</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{masteredWords}}</view>
          <view class="stat-label">已掌握</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{studyDays}}</view>
          <view class="stat-label">学习天数</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="section-title">功能菜单</view>
    <view class="menu-list">
      <view 
        wx:for="{{menuItems}}" 
        wx:key="title"
        class="menu-item"
        data-url="{{item.url}}"
        bindtap="onMenuItemTap"
      >
        <view class="menu-icon">{{item.icon}}</view>
        <view class="menu-content">
          <view class="menu-title">{{item.title}}</view>
          <view class="menu-desc">{{item.desc}}</view>
        </view>
        <view class="menu-arrow">→</view>
      </view>
    </view>
  </view>

  <!-- 其他功能 -->
  <view class="other-section">
    <view class="section-title">其他功能</view>
    <view class="other-list">
      <view class="other-item" bindtap="shareApp">
        <view class="other-icon">📤</view>
        <view class="other-title">分享小程序</view>
        <view class="other-arrow">→</view>
      </view>
      
      <view class="other-item" bindtap="feedback">
        <view class="other-icon">💬</view>
        <view class="other-title">意见反馈</view>
        <view class="other-arrow">→</view>
      </view>
      
      <view class="other-item" bindtap="aboutUs">
        <view class="other-icon">ℹ️</view>
        <view class="other-title">关于我们</view>
        <view class="other-arrow">→</view>
      </view>
    </view>
  </view>

  <!-- 数据提示 -->
  <view class="data-notice">
    <view class="notice-icon">⚠️</view>
    <view class="notice-content">
      <view class="notice-title">数据存储提示</view>
      <view class="notice-text">
        本工具所有学习数据仅保存在您的设备本地，一旦删除本程序或设备故障导致数据丢失，无法通过任何方式找回。
      </view>
    </view>
  </view>

  <!-- 版本信息 -->
  <view class="version-info">
    <text>小柿子识字 v1.0.0</text>
  </view>
</view>
