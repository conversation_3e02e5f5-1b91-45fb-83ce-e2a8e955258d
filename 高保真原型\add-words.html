<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加字词 - 小柿子识字工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="common.css">
</head>
<body class="phone-screen">
            <!-- iOS状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </div>
            </div>
            
            <!-- 小程序导航栏 -->
            <div class="nav-header">
                <i class="nav-back fas fa-chevron-left"></i>
                <span>添加字词</span>
            </div>
            
            <!-- 主内容区域 -->
            <div class="main-content">

                

                
                <!-- 批量导入区域 -->
                <div class="px-4">
                    <div class="card">
                        <h3 class="text-lg font-bold text-gray-800 mb-4">📋 批量添加字词</h3>

                        <div class="space-y-4">
                            <!-- 文本输入 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">字词列表</label>
                                <textarea class="input-field h-32 resize-none" placeholder="请输入字词，支持多种分隔符：&#10;花,草,树,叶&#10;太阳;月亮;星星&#10;水、火、土、石" id="batchTextarea"></textarea>
                                <p class="text-xs text-gray-500 mt-1">支持逗号、分号、顿号等分隔符，系统会自动识别并去重</p>
                            </div>

                            <!-- 导入按钮 -->
                            <button class="btn-primary w-full flex items-center justify-center gap-2" id="batchAddBtn">
                                <i class="fas fa-plus"></i>
                                添加字词
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 今日已添加字词 -->
                <div class="px-4 pb-6">
                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-bold text-gray-800">📚 今日已添加</h3>
                            <span class="text-sm text-gray-500" id="wordCount">5个字词</span>
                        </div>
                        
                        <div class="space-y-2" id="wordList">
                            <!-- 字词列表 -->
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center gap-3">
                                    <span class="text-xl font-bold">花</span>
                                    <span class="text-sm text-gray-600">花朵</span>
                                </div>
                                <button class="text-red-500 text-sm">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                            
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center gap-3">
                                    <span class="text-xl font-bold">草</span>
                                    <span class="text-sm text-gray-600">小草</span>
                                </div>
                                <button class="text-red-500 text-sm">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                            
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center gap-3">
                                    <span class="text-xl font-bold">树</span>
                                    <span class="text-sm text-gray-600">大树</span>
                                </div>
                                <button class="text-red-500 text-sm">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- 开始复习按钮 -->
                        <button class="btn-secondary w-full mt-4 flex items-center justify-center gap-2">
                            <i class="fas fa-play"></i>
                            开始复习这些字词
                        </button>
                    </div>
                </div>
            </div>
    
    <script>
        let wordCount = 3;

        // 批量添加
        document.getElementById('batchAddBtn').addEventListener('click', function() {
            const text = document.getElementById('batchTextarea').value.trim();
            if (text) {
                // 支持多种分隔符：逗号、分号、顿号、换行符
                const separators = /[,;，；、\n]/;
                const words = text.split(separators)
                    .map(w => w.trim())
                    .filter(w => w.length > 0)
                    .filter((word, index, arr) => arr.indexOf(word) === index); // 去重

                words.forEach(word => {
                    addWordToList(word, word);
                });
                document.getElementById('batchTextarea').value = '';
            }

            // 按钮点击效果
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
        
        // 添加字词到列表
        function addWordToList(word, example) {
            wordCount++;
            const wordList = document.getElementById('wordList');
            const wordItem = document.createElement('div');
            wordItem.className = 'flex items-center justify-between p-3 bg-gray-50 rounded-lg fade-in';
            wordItem.innerHTML = `
                <div class="flex items-center gap-3">
                    <span class="text-xl font-bold">${word}</span>
                    <span class="text-sm text-gray-600">${example}</span>
                </div>
                <button class="text-red-500 text-sm delete-btn">
                    <i class="fas fa-trash"></i>
                </button>
            `;
            
            // 添加删除事件
            wordItem.querySelector('.delete-btn').addEventListener('click', function() {
                wordItem.remove();
                wordCount--;
                updateWordCount();
            });
            
            wordList.appendChild(wordItem);
            updateWordCount();
        }
        
        // 更新字词数量
        function updateWordCount() {
            document.getElementById('wordCount').textContent = `${wordCount}个字词`;
        }
        
        // 删除按钮事件
        document.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.closest('.flex').remove();
                wordCount--;
                updateWordCount();
            });
        });
        
        // 通用按钮点击效果
        document.querySelectorAll('button').forEach(button => {
            button.addEventListener('click', function() {
                if (!this.style.transform) {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                }
            });
        });
        
        // 返回按钮
        document.querySelector('.nav-back').addEventListener('click', function() {
            this.style.transform = 'scale(0.9)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    </script>
</body>
</html>
