/* 学习记录页面样式 */

.records-container {
  background: var(--warm-beige);
  min-height: 100vh;
  padding: 32rpx;
}

/* 幼儿信息头部 */
.child-header {
  display: flex;
  align-items: center;
  gap: 24rpx;
  background: linear-gradient(135deg, var(--primary-green), var(--light-green));
  color: white;
  padding: 32rpx;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  box-shadow: var(--shadow-soft);
}

.child-avatar {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.child-info {
  flex: 1;
}

.child-name {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.child-meta {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 统计摘要卡片 */
.summary-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: var(--shadow-soft);
  border: 1px solid var(--border-light);
}

.summary-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 32rpx;
  text-align: center;
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.summary-number {
  font-size: 40rpx;
  font-weight: bold;
  color: var(--primary-green);
}

.summary-label {
  font-size: 24rpx;
  color: var(--text-light);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.month-count {
  background: var(--light-green);
  color: var(--text-dark);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.export-btn {
  color: var(--primary-green);
  font-size: 24rpx;
  font-weight: 500;
  padding: 8rpx 16rpx;
  border: 1px solid var(--primary-green);
  border-radius: 20rpx;
}

/* 本月日历 */
.month-calendar {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140rpx, 1fr));
  gap: 16rpx;
}

.calendar-day {
  background: var(--soft-green);
  border-radius: 16rpx;
  padding: 20rpx;
  text-align: center;
  border: 1px solid var(--light-green);
  transition: all 0.3s ease;
}

.calendar-day:active {
  transform: scale(0.95);
  background: var(--light-green);
}

.day-date {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 4rpx;
}

.day-weekday {
  font-size: 20rpx;
  color: var(--text-light);
  margin-bottom: 12rpx;
}

.day-stats {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.day-accuracy {
  font-size: 24rpx;
  font-weight: 600;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
}

.day-accuracy.good {
  background: var(--primary-green);
  color: white;
}

.day-accuracy.normal {
  background: var(--soft-orange);
  color: white;
}

.day-accuracy.poor {
  background: #ffcdd2;
  color: #d32f2f;
}

.day-words {
  font-size: 20rpx;
  color: var(--text-light);
}

/* 记录列表 */
.records-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.record-item {
  background: var(--warm-beige);
  border-radius: 16rpx;
  overflow: hidden;
  border: 1px solid var(--border-light);
}

.record-main {
  display: flex;
  padding: 24rpx;
  gap: 24rpx;
}

.record-date {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 120rpx;
  background: white;
  border-radius: 12rpx;
  padding: 16rpx;
  border: 1px solid var(--border-light);
}

.date-text {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 4rpx;
}

.weekday-text {
  font-size: 20rpx;
  color: var(--text-light);
}

.record-stats {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.stats-row {
  display: flex;
  gap: 24rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  min-width: 80rpx;
}

.stat-label {
  font-size: 20rpx;
  color: var(--text-light);
}

.stat-value {
  font-size: 24rpx;
  font-weight: 600;
  color: var(--text-dark);
}

.stat-value.accuracy.good {
  color: var(--primary-green);
}

.stat-value.accuracy.normal {
  color: var(--soft-orange);
}

.stat-value.accuracy.poor {
  color: #d32f2f;
}

.weak-words {
  background: #fff3e0;
  padding: 12rpx;
  border-radius: 8rpx;
  border: 1px solid #ffe0b2;
}

.weak-label {
  font-size: 20rpx;
  color: var(--soft-orange);
  font-weight: 500;
}

.weak-list {
  font-size: 22rpx;
  color: #e65100;
}

.record-actions {
  border-top: 1px solid var(--border-light);
  padding: 16rpx 24rpx;
  text-align: right;
}

.delete-btn {
  background: transparent;
  color: #ff4444;
  border: 1px solid #ff4444;
  border-radius: 16rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.delete-btn:active {
  background: #ffebee;
}

/* 空状态 */
.empty-records {
  text-align: center;
  padding: 80rpx 32rpx;
}

.empty-icon {
  font-size: 100rpx;
  margin-bottom: 24rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: var(--text-light);
  line-height: 1.5;
}

/* 使用说明 */
.usage-tips {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-top: 32rpx;
  box-shadow: var(--shadow-soft);
  border: 1px solid var(--border-light);
}

.tips-title {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.tip-item {
  font-size: 24rpx;
  color: var(--text-light);
  line-height: 1.4;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .summary-grid {
    grid-template-columns: 1fr;
    gap: 24rpx;
  }
  
  .month-calendar {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .record-main {
    flex-direction: column;
    gap: 16rpx;
  }
  
  .record-date {
    align-self: flex-start;
    min-width: auto;
  }
  
  .stats-row {
    flex-wrap: wrap;
    gap: 16rpx;
  }
}
